#!/usr/bin/env python3
"""
测试PIDB机柜数据读取功能
验证从PIDB cabinet工作表正确读取真实机柜名称
"""

import sys
import os
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pidb_cabinet_reading():
    """测试PIDB机柜数据读取"""
    print("=== 测试PIDB机柜数据读取功能 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        # 创建数据加载器
        config = {}
        data_loader = DataLoader(config)
        
        # 创建模拟的PIDB cabinet工作表数据（基于提供的截图）
        cabinet_data = {
            'Cabinet': ['1105-SIS-SYS-101', '1105-SIS-BAR-101', '1105-SIS-REL-101', '1105-SIS-RIO-001'],
            'CabinetTemplate': ['PPG SYS', 'PPG BAR', 'PPG REL', 'PPG RIO'],
            'CabinetNo': ['01', '02', '03', '01']
        }
        
        # 创建DataFrame模拟PIDB数据
        df = pd.DataFrame(cabinet_data)
        
        print("模拟的PIDB cabinet工作表数据:")
        print(df.to_string(index=False))
        
        # 测试机柜数据提取
        print(f"\n开始测试机柜数据提取...")
        cabinets = data_loader._extract_cabinets_from_pidb(df, 'cabinet')
        
        print(f"\n=== 提取结果分析 ===")
        print(f"提取的机柜数量: {len(cabinets)}")
        
        if cabinets:
            print(f"\n提取的机柜详情:")
            for i, cabinet in enumerate(cabinets, 1):
                print(f"  机柜 {i}:")
                print(f"    真实名称: {cabinet['name']}")
                print(f"    模板名称: {cabinet['template']}")
                print(f"    机柜类型: {cabinet['type']}")
                print(f"    机柜编号: {cabinet.get('cabinet_no', 'N/A')}")
                print(f"    数据来源: {cabinet.get('source', 'Unknown')}")
                
                # 验证映射关系
                expected_mapping = {
                    'PPG SYS': '1105-SIS-SYS-101',
                    'PPG BAR': '1105-SIS-BAR-101', 
                    'PPG REL': '1105-SIS-REL-101',
                    'PPG RIO': '1105-SIS-RIO-001'
                }
                
                template = cabinet['template']
                actual_name = cabinet['name']
                expected_name = expected_mapping.get(template)
                
                if expected_name and actual_name == expected_name:
                    print(f"    ✅ 映射关系正确: {template} -> {actual_name}")
                else:
                    print(f"    ❌ 映射关系错误: {template} -> {actual_name} (期望: {expected_name})")
        
        return len(cabinets) == 4  # 期望提取4个机柜
        
    except Exception as e:
        print(f"❌ PIDB机柜数据读取测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_column_mapping():
    """测试列名映射功能"""
    print("\n=== 测试列名映射功能 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        data_loader = DataLoader({})
        
        # 测试不同的列名格式
        test_cases = [
            ['Cabinet', 'CabinetTemplate', 'CabinetNo'],  # 标准格式
            ['cabinet', 'template', 'no'],  # 小写格式
            ['Name', 'Type', 'Number'],  # 替代格式
            ['CabinetName', 'cabinet_template', 'cabinet_no']  # 混合格式
        ]
        
        for i, columns in enumerate(test_cases, 1):
            print(f"\n测试用例 {i}: {columns}")
            mapping = data_loader._build_cabinet_column_mapping(columns)
            print(f"  映射结果: {mapping}")
            
            # 验证映射结果
            has_cabinet_name = 'cabinet_name' in mapping
            has_template = 'template' in mapping
            
            if has_cabinet_name:
                print(f"  ✅ 找到机柜名称列: {mapping['cabinet_name']}")
            else:
                print(f"  ❌ 未找到机柜名称列")
            
            if has_template:
                print(f"  ✅ 找到模板列: {mapping['template']}")
            else:
                print(f"  ⚠️  未找到模板列")
        
        return True
        
    except Exception as e:
        print(f"❌ 列名映射测试失败: {e}")
        return False

def test_cabinet_type_inference():
    """测试机柜类型推断功能"""
    print("\n=== 测试机柜类型推断功能 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        data_loader = DataLoader({})
        
        # 测试不同模板名称的类型推断
        test_cases = [
            ('PPG SYS', 'System'),
            ('PPG BAR', 'Marshalling'),
            ('PPG REL', 'Marshalling'),
            ('PPG RIO', 'System/Marshalling'),
            ('Unknown', 'Marshalling')  # 默认类型
        ]
        
        success_count = 0
        for template, expected_type in test_cases:
            inferred_type = data_loader._infer_cabinet_type_from_template(template)
            print(f"  {template} -> {inferred_type} (期望: {expected_type})")
            
            if inferred_type == expected_type:
                print(f"    ✅ 类型推断正确")
                success_count += 1
            else:
                print(f"    ❌ 类型推断错误")
        
        print(f"\n类型推断成功率: {success_count}/{len(test_cases)}")
        return success_count == len(test_cases)
        
    except Exception as e:
        print(f"❌ 机柜类型推断测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试PIDB机柜数据读取功能...")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_column_mapping())
    test_results.append(test_cabinet_type_inference())
    test_results.append(test_pidb_cabinet_reading())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 最终测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！PIDB机柜数据读取功能实现成功。")
        print("\n🔧 实现的功能特性：")
        print("✅ 1. 正确读取PIDB cabinet工作表")
        print("✅ 2. 建立模板名称到真实名称的映射关系")
        print("✅ 3. 支持多种列名格式的自动识别")
        print("✅ 4. 智能推断机柜类型")
        print("✅ 5. 保留完整的原始数据")
        
        print("\n📋 修复效果：")
        print("现在系统将从PIDB读取真实机柜名称：")
        print("✅ PPG SYS -> 1105-SIS-SYS-101")
        print("✅ PPG BAR -> 1105-SIS-BAR-101")
        print("✅ PPG REL -> 1105-SIS-REL-101")
        print("✅ PPG RIO -> 1105-SIS-RIO-001")
        
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
