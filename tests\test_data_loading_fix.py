"""
测试修复后的数据加载功能
"""

import json
from pathlib import Path
from core.data_loader_simple import DataLoader
from utils.config_manager_simple import Config<PERSON>anager


def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试修复后的数据加载功能 ===")
    
    # 加载配置
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 创建数据加载器
    data_loader = DataLoader(config)
    
    print("\n1. 测试机柜配置加载...")
    try:
        cabinet_profiles = data_loader.load_cabinet_profiles()
        print(f"✓ 机柜配置加载成功: {len(cabinet_profiles)} 个配置文件")
        for name, profile in cabinet_profiles.items():
            rails_count = len(profile.get('rails', []))
            racks_count = len(profile.get('racks', []))
            print(f"  - {name}: {rails_count} 个导轨, {racks_count} 个机架")
    except Exception as e:
        print(f"✗ 机柜配置加载失败: {e}")
    
    print("\n2. 测试典型回路加载...")
    try:
        wiring_typicals = data_loader.load_wiring_typicals()
        print(f"✓ 典型回路加载成功: {len(wiring_typicals)} 个回路文件")
        for name, typical in wiring_typicals.items():
            components_count = len(typical.get('components', []))
            connections_count = len(typical.get('connections', []))
            print(f"  - {name}: {components_count} 个组件, {connections_count} 个连接")
    except Exception as e:
        print(f"✗ 典型回路加载失败: {e}")
    
    print("\n3. 测试IODB数据加载...")
    try:
        iodb_file = Path("04A_IODB/IODB_nospare.xlsx")
        if iodb_file.exists():
            iodb_data = data_loader.load_iodb_data(str(iodb_file))
            io_points_count = len(iodb_data.get('io_points', []))
            cables_count = len(iodb_data.get('cables', {}))
            print(f"✓ IODB数据加载成功: {io_points_count} 个I/O点, {cables_count} 条电缆")
            
            # 显示电缆信息
            if cables_count > 0:
                print("  电缆信息:")
                for cable_name, cable_info in list(iodb_data['cables'].items())[:5]:
                    points_in_cable = len(cable_info.get('io_points', []))
                    pair_size = cable_info.get('pair_size', 0)
                    print(f"    - {cable_name}: {points_in_cable} 个点, 对数: {pair_size}")
                if cables_count > 5:
                    print(f"    ... 还有 {cables_count - 5} 条电缆")
        else:
            print(f"✗ IODB文件不存在: {iodb_file}")
    except Exception as e:
        print(f"✗ IODB数据加载失败: {e}")
    
    print("\n4. 测试PIDB数据加载...")
    try:
        pidb_file = Path("04B_PIDB/PIDB PPG.xlsx")
        if pidb_file.exists():
            pidb_data = data_loader.load_pidb_data(str(pidb_file))
            racks_count = len(pidb_data.get('racks', []))
            cabinets_count = len(pidb_data.get('cabinets', []))
            print(f"✓ PIDB数据加载成功: {racks_count} 个机架, {cabinets_count} 个机柜")
            
            # 显示机柜信息
            if cabinets_count > 0:
                print("  机柜信息:")
                for cabinet in pidb_data['cabinets']:
                    cabinet_name = cabinet.get('name', 'Unknown')
                    cabinet_type = cabinet.get('type', 'Unknown')
                    print(f"    - {cabinet_name}: {cabinet_type}")
            
            # 显示机架信息
            if racks_count > 0:
                print("  机架信息:")
                for rack in pidb_data['racks'][:5]:
                    rack_name = rack.get('name', 'Unknown')
                    cabinet = rack.get('cabinet', 'Unknown')
                    max_slots = rack.get('max_slots', 0)
                    print(f"    - {rack_name} (机柜: {cabinet}, 槽位: {max_slots})")
                if racks_count > 5:
                    print(f"    ... 还有 {racks_count - 5} 个机架")
        else:
            print(f"✗ PIDB文件不存在: {pidb_file}")
    except Exception as e:
        print(f"✗ PIDB数据加载失败: {e}")
    
    print("\n5. 测试完整数据加载...")
    try:
        all_data = data_loader.load_all_data()
        load_status = all_data.get('load_status', {})
        
        print("✓ 完整数据加载测试:")
        print(f"  - 机柜配置: {'成功' if load_status.get('cabinet_profiles') else '失败'}")
        print(f"  - 典型回路: {'成功' if load_status.get('wiring_typicals') else '失败'}")
        
        # 显示摘要
        cabinet_profiles = all_data.get('cabinet_profiles', {})
        wiring_typicals = all_data.get('wiring_typicals', {})
        
        print(f"\n数据加载摘要:")
        print(f"  - 机柜配置: {len(cabinet_profiles)} 个配置文件")
        print(f"  - 典型回路: {len(wiring_typicals)} 个回路文件")
        
    except Exception as e:
        print(f"✗ 完整数据加载失败: {e}")
    
    print("\n=== 数据加载测试完成 ===")


if __name__ == "__main__":
    test_data_loading()
