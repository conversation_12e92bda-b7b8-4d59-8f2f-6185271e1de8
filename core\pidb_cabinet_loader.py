"""
PIDB实际机柜加载器
从PIDB Excel文件中加载实际机柜实例信息
"""

import pandas as pd
from typing import Dict, List, Any, Optional
from pathlib import Path

from core.logger import get_logger
from core.component_allocation_models import ActualCabinet, RailInstance


class PIDBCabinetLoader:
    """PIDB实际机柜加载器"""
    
    def __init__(self, pidb_directory: str = "04B_PIDB"):
        self.pidb_directory = Path(pidb_directory)
        self.logger = get_logger(__name__)
    
    def load_actual_cabinets(self, cabinet_templates: Dict[str, Any]) -> List[ActualCabinet]:
        """
        从PIDB文件加载实际机柜实例
        
        Args:
            cabinet_templates: 机柜模板字典
            
        Returns:
            实际机柜实例列表
        """
        actual_cabinets = []
        
        try:
            # 查找PIDB Excel文件
            pidb_files = list(self.pidb_directory.glob("*.xlsx"))
            if not pidb_files:
                self.logger.warning(f"未在 {self.pidb_directory} 中找到PIDB Excel文件")
                return actual_cabinets
            
            # 使用第一个找到的PIDB文件
            pidb_file = pidb_files[0]
            self.logger.info(f"加载PIDB文件: {pidb_file}")
            
            # 读取cabinet工作表
            cabinet_data = self._read_cabinet_worksheet(pidb_file)
            
            # 为每个实际机柜创建实例
            for cabinet_row in cabinet_data:
                try:
                    actual_cabinet = self._create_actual_cabinet(cabinet_row, cabinet_templates)
                    if actual_cabinet:
                        actual_cabinets.append(actual_cabinet)
                except Exception as e:
                    self.logger.error(f"创建实际机柜失败 {cabinet_row.get('name', 'Unknown')}: {e}")
            
            self.logger.info(f"成功加载 {len(actual_cabinets)} 个实际机柜")
            return actual_cabinets
            
        except Exception as e:
            self.logger.error(f"加载PIDB实际机柜失败: {e}")
            return actual_cabinets
    
    def _read_cabinet_worksheet(self, pidb_file: Path) -> List[Dict[str, Any]]:
        """
        读取PIDB文件中的cabinet工作表
        
        Args:
            pidb_file: PIDB Excel文件路径
            
        Returns:
            机柜数据列表
        """
        try:
            # 尝试读取名为'cabinet'的工作表
            df = pd.read_excel(pidb_file, sheet_name='cabinet')
            
            # 转换为字典列表
            cabinet_data = []
            for _, row in df.iterrows():
                cabinet_info = {
                    'name': str(row.get('name', row.get('Name', ''))),
                    'template': str(row.get('template', row.get('Template', ''))),
                    'location': str(row.get('location', row.get('Location', ''))),
                    'type': str(row.get('type', row.get('Type', 'Marshalling'))),
                    'description': str(row.get('description', row.get('Description', ''))),
                    'properties': row.to_dict()  # 保存所有原始数据
                }
                
                # 过滤掉空的机柜名称
                if cabinet_info['name'] and cabinet_info['name'] != 'nan':
                    cabinet_data.append(cabinet_info)
            
            self.logger.debug(f"从cabinet工作表读取了 {len(cabinet_data)} 条机柜记录")
            return cabinet_data
            
        except Exception as e:
            self.logger.error(f"读取cabinet工作表失败: {e}")
            
            # 尝试其他可能的工作表名称
            try:
                excel_file = pd.ExcelFile(pidb_file)
                sheet_names = excel_file.sheet_names
                self.logger.info(f"可用的工作表: {sheet_names}")
                
                # 尝试找到包含'cabinet'的工作表
                cabinet_sheet = None
                for sheet_name in sheet_names:
                    if 'cabinet' in sheet_name.lower():
                        cabinet_sheet = sheet_name
                        break
                
                if cabinet_sheet:
                    self.logger.info(f"尝试使用工作表: {cabinet_sheet}")
                    df = pd.read_excel(pidb_file, sheet_name=cabinet_sheet)
                    return self._process_cabinet_dataframe(df)
                
            except Exception as e2:
                self.logger.error(f"尝试其他工作表也失败: {e2}")
            
            return []
    
    def _process_cabinet_dataframe(self, df: pd.DataFrame) -> List[Dict[str, Any]]:
        """处理机柜数据框"""
        cabinet_data = []
        
        for _, row in df.iterrows():
            # 尝试不同的列名组合
            name = self._get_column_value(row, ['name', 'Name', 'CABINET_NAME', 'Cabinet'])
            template = self._get_column_value(row, ['template', 'Template', 'TEMPLATE', 'Type'])
            location = self._get_column_value(row, ['location', 'Location', 'LOCATION'])
            
            if name:
                cabinet_info = {
                    'name': str(name),
                    'template': str(template) if template else 'Unknown',
                    'location': str(location) if location else '',
                    'type': 'Marshalling',
                    'description': '',
                    'properties': row.to_dict()
                }
                cabinet_data.append(cabinet_info)
        
        return cabinet_data
    
    def _get_column_value(self, row: pd.Series, possible_columns: List[str]) -> Optional[str]:
        """从可能的列名中获取值"""
        for col in possible_columns:
            if col in row.index and pd.notna(row[col]):
                return str(row[col])
        return None
    
    def _create_actual_cabinet(self, cabinet_data: Dict[str, Any], 
                             cabinet_templates: Dict[str, Any]) -> Optional[ActualCabinet]:
        """
        创建实际机柜实例
        
        Args:
            cabinet_data: 机柜数据
            cabinet_templates: 机柜模板字典
            
        Returns:
            实际机柜实例
        """
        try:
            actual_name = cabinet_data['name']
            template_name = cabinet_data['template']
            
            # 查找机柜模板
            template = cabinet_templates.get(template_name)
            if not template:
                # 尝试模糊匹配
                template = self._find_template_by_fuzzy_match(template_name, cabinet_templates)
                if not template:
                    self.logger.warning(f"未找到机柜模板: {template_name} (实际机柜: {actual_name})")
                    return None
            
            # 创建导轨实例
            rails = self._create_rail_instances_from_template(template)
            
            actual_cabinet = ActualCabinet(
                name=actual_name,
                template_name=template_name,
                location=cabinet_data['location'],
                cabinet_type=cabinet_data['type'],
                rails=rails,
                properties=cabinet_data['properties']
            )
            
            self.logger.debug(f"创建实际机柜: {actual_name} (模板: {template_name}, {len(rails)}个导轨)")
            return actual_cabinet
            
        except Exception as e:
            self.logger.error(f"创建实际机柜失败: {e}")
            return None
    
    def _find_template_by_fuzzy_match(self, template_name: str, 
                                    cabinet_templates: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """通过模糊匹配查找机柜模板"""
        template_name_lower = template_name.lower()
        
        # 尝试部分匹配
        for name, template in cabinet_templates.items():
            if template_name_lower in name.lower() or name.lower() in template_name_lower:
                self.logger.info(f"模糊匹配机柜模板: {template_name} -> {name}")
                return template
        
        return None
    
    def _create_rail_instances_from_template(self, template: Dict[str, Any]) -> List[RailInstance]:
        """从模板创建导轨实例"""
        rails = []
        
        template_rails = template.get('rails', [])
        for rail_template in template_rails:
            try:
                # 提取导轨属性
                rail_name = rail_template.get('name', '')
                position = rail_template.get('position', '')
                length = float(rail_template.get('length', 1000))
                io_type = rail_template.get('io_type', 'Mixed')
                intrinsic = rail_template.get('intrinsic', 'NIS')
                voltage_level = int(rail_template.get('voltage_level', 24))
                
                # 提取支持的器件类型
                supported_part_types = []
                for i in range(1, 10):  # 检查PartType01到PartType09
                    part_type_key = f'PartType{i:02d}'
                    part_type = rail_template.get(part_type_key, '')
                    if part_type and part_type.strip():
                        supported_part_types.append(part_type.strip())
                
                rail = RailInstance(
                    name=rail_name,
                    position=position,
                    length=length,
                    io_type=io_type,
                    intrinsic=intrinsic,
                    voltage_level=voltage_level,
                    supported_part_types=supported_part_types,
                    reserved_from=float(rail_template.get('reserved_from', 0)),
                    reserved_to=float(rail_template.get('reserved_to', 0))
                )
                
                rails.append(rail)
                
            except Exception as e:
                self.logger.warning(f"创建导轨实例失败: {e}")
        
        return rails
