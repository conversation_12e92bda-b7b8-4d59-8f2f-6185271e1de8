#!/usr/bin/env python3
"""
检查Excel文件结构的脚本
"""

import pandas as pd
from pathlib import Path

def check_excel_structure():
    """检查PIDB Excel文件的结构"""
    print("=== 检查PIDB Excel文件结构 ===")
    
    # 查找PIDB文件
    pidb_files = list(Path("data/pidb").glob("*.xlsx"))
    if not pidb_files:
        print("❌ 未找到PIDB文件")
        return
    
    for pidb_file in pidb_files:
        print(f"\n📁 检查文件: {pidb_file}")
        
        try:
            # 获取所有工作表名称
            excel_file = pd.ExcelFile(pidb_file)
            sheet_names = excel_file.sheet_names
            print(f"   工作表数量: {len(sheet_names)}")
            print(f"   工作表名称: {sheet_names}")
            
            # 检查每个工作表的内容
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(pidb_file, sheet_name=sheet_name)
                    print(f"\n   工作表: {sheet_name}")
                    print(f"     行数: {len(df)}")
                    print(f"     列数: {len(df.columns)}")
                    print(f"     列名: {list(df.columns)}")
                    
                    # 显示前几行数据
                    if len(df) > 0:
                        print(f"     前3行数据:")
                        for i, (_, row) in enumerate(df.head(3).iterrows()):
                            print(f"       行{i+1}: {dict(row)}")
                    
                except Exception as e:
                    print(f"     ❌ 读取工作表 {sheet_name} 失败: {e}")
            
        except Exception as e:
            print(f"   ❌ 读取文件失败: {e}")

if __name__ == "__main__":
    check_excel_structure()
