"""
基于典型回路的多器件分配引擎
实现I/O点到多个器件的精确分配
"""

from typing import Dict, List, Optional, Any, Tuple
from pathlib import Path

from core.logger import get_logger
from core.component_allocation_models import (
    ComponentDefinition, ComponentAllocation, ActualCabinet, RailInstance,
    MultiComponentAllocationResult, ComponentType, MountingType
)
from core.wiring_typical_parser import WiringTypicalManager
from core.data_models import IOPoint
from core.naming_engine import NamingEngine, NamingContext, NamingElementType
from core.pidb_cabinet_loader import PIDBCabinetLoader
from core.wire_connection_manager import WireConnectionManager


class ComponentAllocationEngine:
    """基于典型回路的器件分配引擎"""

    def __init__(self, config: Dict[str, Any], wiring_typical_directory: str = "01C_Wiring Typical"):
        self.logger = get_logger(__name__)
        self.config = config
        self.typical_manager = WiringTypicalManager(wiring_typical_directory)

        # 初始化命名引擎
        self.naming_engine = NamingEngine(config)

        # 初始化PIDB加载器
        self.pidb_loader = PIDBCabinetLoader()

        # 加载所有典型回路（只加载一次）
        self.wiring_typicals = self.typical_manager.load_all_typicals()
        self.logger.info(f"典型回路加载完成: {len(self.wiring_typicals)} 个")

        # 初始化Wire连接管理器，共享典型回路管理器
        self.wire_manager = WireConnectionManager(self.typical_manager)

        self.logger.info(f"分配引擎初始化完成，加载了 {len(self.wiring_typicals)} 个典型回路")
    
    def allocate_io_point_components(self,
                                   io_point: IOPoint,
                                   actual_cabinet: ActualCabinet,
                                   wiring_typicals: Dict[str, Any],
                                   available_cabinets: Optional[List[ActualCabinet]] = None) -> MultiComponentAllocationResult:
        """
        为单个I/O点分配所有必需的器件

        Args:
            io_point: I/O点对象
            actual_cabinet: 首选机柜实例
            wiring_typicals: 典型回路字典（兼容性参数，实际使用内部管理器）
            available_cabinets: 可用机柜列表，用于智能机柜选择

        Returns:
            多器件分配结果
        """
        result = MultiComponentAllocationResult(io_point=io_point)
        io_tag = getattr(io_point, 'tag', 'Unknown')

        try:
            # 1. 获取I/O点的典型回路
            typical_name = getattr(io_point, 'wiring_typical', None)
            if not typical_name:
                result.success = False
                error_msg = f"[I/O点: {io_tag}] 没有典型回路信息"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
                return result

            # 记录分配开始信息
            self.logger.info(f"开始分配I/O点 {io_tag}，使用典型回路: {typical_name}，目标机柜: {actual_cabinet.name}")
            
            # 2. 获取典型回路定义
            typical_def = self.typical_manager.get_typical_by_name(typical_name)
            if not typical_def:
                result.success = False
                error_msg = f"[I/O点: {io_tag}] 未找到典型回路: {typical_name}"
                result.errors.append(error_msg)
                self.logger.error(error_msg)
                return result

            # 3. 获取需要分配的器件清单
            marshalling_components = typical_def.get_marshalling_components()
            if not marshalling_components:
                warning_msg = f"[I/O点: {io_tag}] 典型回路 {typical_name} 没有定义汇控柜器件"
                result.warnings.append(warning_msg)
                self.logger.warning(warning_msg)
                return result

            # 4. 智能机柜选择：如果提供了机柜列表，尝试找到最适合的机柜
            selected_cabinet = actual_cabinet
            if available_cabinets:
                suitable_cabinet = self._find_suitable_cabinet(marshalling_components, available_cabinets, io_point)
                if suitable_cabinet:
                    selected_cabinet = suitable_cabinet
                    self.logger.info(f"[I/O点: {io_tag}] 智能选择机柜: {selected_cabinet.name} (原机柜: {actual_cabinet.name})")
                else:
                    self.logger.warning(f"[I/O点: {io_tag}] 未找到合适的机柜，使用原机柜: {actual_cabinet.name}")

            self.logger.debug(f"[I/O点: {io_tag}] 使用机柜: {selected_cabinet.name} ({selected_cabinet.cabinet_type})")

            self.logger.info(f"[I/O点: {io_tag}] 需要分配 {len(marshalling_components)} 个器件: {[comp.name for comp in marshalling_components]}")
            
            # 5. 为每个器件分配位置
            for i, component in enumerate(marshalling_components, 1):
                try:
                    self.logger.debug(f"[I/O点: {io_tag}] 正在分配器件 {i}/{len(marshalling_components)}: {component.name} ({component.component_type.value})")

                    # 验证器件与机柜类型的兼容性
                    if not self._validate_component_cabinet_compatibility(component, selected_cabinet):
                        required_type = self._determine_required_cabinet_type(component)
                        error_msg = f"[I/O点: {io_tag}] 器件 {component.name} ({component.component_type.value}) 需要 {required_type} 类型机柜，但当前机柜 {selected_cabinet.name} 是 {selected_cabinet.cabinet_type} 类型"
                        result.errors.append(error_msg)
                        self.logger.error(error_msg)
                        result.success = False
                        continue

                    allocation = self._allocate_single_component(
                        component, selected_cabinet, io_point
                    )
                    if allocation:
                        result.component_allocations.append(allocation)
                        self.logger.debug(f"[I/O点: {io_tag}] 器件 {component.name} 分配成功，位置: {allocation.rail_name}[{allocation.position_start:.1f}-{allocation.position_end:.1f}mm]")
                    else:
                        error_msg = f"[I/O点: {io_tag}] 器件 {component.name} 分配失败"
                        result.errors.append(error_msg)
                        self.logger.error(error_msg)
                        result.success = False

                except Exception as e:
                    error_msg = f"[I/O点: {io_tag}] 器件 {component.name} 分配异常: {e}"
                    result.errors.append(error_msg)
                    self.logger.error(error_msg)
                    result.success = False
            
            # 5. 创建Wire连接信息
            if result.success and result.component_allocations:
                try:
                    wire_connections = self.wire_manager.create_wire_connections_for_io_point(
                        io_point, result.component_allocations
                    )

                    # 将Wire连接信息添加到分配结果中
                    for allocation in result.component_allocations:
                        allocation.wire_connections = [conn.to_csv_dict() for conn in wire_connections]

                    self.logger.debug(f"[I/O点: {io_tag}] 创建了 {len(wire_connections)} 个Wire连接")

                except Exception as e:
                    warning_msg = f"[I/O点: {io_tag}] 创建Wire连接信息失败: {e}"
                    result.warnings.append(warning_msg)
                    self.logger.warning(warning_msg)

            # 6. 验证分配结果
            if result.success and result.component_allocations:
                self.logger.info(f"[I/O点: {io_tag}] 分配完成，成功分配 {len(result.component_allocations)} 个器件到机柜 {selected_cabinet.name}")
            elif not result.component_allocations:
                result.success = False
                error_msg = f"[I/O点: {io_tag}] 没有成功分配任何器件"
                result.errors.append(error_msg)
                self.logger.error(error_msg)

            return result

        except Exception as e:
            error_msg = f"[I/O点: {io_tag}] 多器件分配异常: {e}"
            self.logger.error(error_msg)
            result.success = False
            result.errors.append(f"分配过程异常: {e}")
            return result
    
    def _allocate_single_component(self,
                                 component: ComponentDefinition,
                                 actual_cabinet: ActualCabinet,
                                 io_point: IOPoint) -> Optional[ComponentAllocation]:
        """
        分配单个器件到机柜中的合适位置

        Args:
            component: 器件定义
            actual_cabinet: 实际机柜
            io_point: I/O点对象

        Returns:
            器件分配结果，如果分配失败返回None
        """
        io_tag = getattr(io_point, 'tag', 'Unknown')

        try:
            # 1. 查找兼容的导轨
            compatible_rails = actual_cabinet.find_compatible_rails(component)
            if not compatible_rails:
                self.logger.warning(f"[I/O点: {io_tag}] 器件 {component.name} 在机柜 {actual_cabinet.name} 中无兼容导轨")
                return None
            
            # 2. 选择最佳导轨（当前策略：选择第一个有足够空间的）
            target_rail = self._select_best_rail(compatible_rails, component)
            if not target_rail:
                self.logger.warning(f"[I/O点: {io_tag}] 器件 {component.name} 在机柜 {actual_cabinet.name} 中无可用空间")
                return None

            self.logger.debug(f"[I/O点: {io_tag}] 器件 {component.name} 选择导轨 {target_rail.name}，可用空间: {target_rail.available_length:.1f}mm")
            
            # 3. 在导轨上分配器件
            allocation = target_rail.allocate_component(component, actual_cabinet.name)

            # 4. 计算器件在导轨上的序号
            component_sequence = self._calculate_component_sequence_on_rail(target_rail, allocation)

            # 5. 生成器件名称
            component_name = self._generate_component_name(
                component, actual_cabinet, target_rail, component_sequence, io_point
            )

            # 6. 更新分配信息
            allocation.component_name = component_name
            allocation.hardware_type = component.component_type.value
            allocation.part_number = component.part_number
            allocation.sequence_on_rail = component_sequence

            # 7. 提取和设置ScrewID（从典型回路的Wire信息中获取）
            screw_id = self._extract_screw_id_for_component(component, io_point)
            allocation.screw_id = screw_id

            # 8. 为特定类型的器件分配额外信息
            self._assign_component_specific_info(allocation, component, io_point)

            self.logger.debug(f"[I/O点: {io_tag}] 器件 {component.name} 分配完成 -> 机柜: {actual_cabinet.name}, 导轨: {target_rail.name}, 名称: {component_name}")
            return allocation

        except Exception as e:
            self.logger.error(f"[I/O点: {io_tag}] 分配器件 {component.name} 异常: {e}")
            return None
    
    def _select_best_rail(self, 
                         compatible_rails: List[RailInstance],
                         component: ComponentDefinition) -> Optional[RailInstance]:
        """
        从兼容的导轨中选择最佳的一个
        
        当前策略：
        1. 优先选择有足够空间的导轨
        2. 在多个可选导轨中，选择剩余空间最多的
        
        Args:
            compatible_rails: 兼容的导轨列表
            component: 器件定义
            
        Returns:
            选中的导轨，如果没有合适的返回None
        """
        suitable_rails = []
        
        # 筛选有足够空间的导轨
        for rail in compatible_rails:
            if rail.can_accommodate(component):
                suitable_rails.append(rail)
        
        if not suitable_rails:
            return None
        
        # 选择剩余空间最多的导轨
        best_rail = max(suitable_rails, key=lambda r: r.available_length)
        return best_rail
    
    def _assign_component_specific_info(self,
                                      allocation: ComponentAllocation,
                                      component: ComponentDefinition,
                                      io_point: IOPoint) -> None:
        """
        为特定类型的器件分配额外的信息

        Args:
            allocation: 器件分配结果
            component: 器件定义
            io_point: I/O点对象
        """
        io_tag = getattr(io_point, 'tag', 'Unknown')

        try:
            if component.component_type == ComponentType.ETP:
                # ETP器件需要分配槽位和通道
                self._assign_etp_slot_and_channel(allocation, component, io_point)

            elif component.component_type in [ComponentType.FIELD_TERM_IN, ComponentType.FIELD_TERM_OUT]:
                # 端子排需要分配端子号
                self._assign_terminal_numbers(allocation, component, io_point)

            elif component.component_type == ComponentType.BARRIER:
                # 安全栅可能需要特殊处理
                self._assign_barrier_info(allocation, component, io_point)

        except Exception as e:
            self.logger.warning(f"[I/O点: {io_tag}] 分配器件特定信息失败 {component.name}: {e}")
    
    def _assign_etp_slot_and_channel(self,
                                   allocation: ComponentAllocation,
                                   component: ComponentDefinition,
                                   io_point: IOPoint) -> None:
        """为ETP器件分配槽位和通道"""
        io_tag = getattr(io_point, 'tag', 'Unknown')

        # 简化实现：分配第一个可用的槽位和通道
        if component.slots:
            allocation.slot = component.slots[0]  # 使用第一个槽位

        # 分配通道（这里需要更复杂的逻辑来管理通道占用）
        allocation.channel = 1  # 简化为第一个通道

        self.logger.debug(f"[I/O点: {io_tag}] ETP器件 {component.name} 分配槽位: {allocation.slot}, 通道: {allocation.channel}")
    
    def _assign_terminal_numbers(self,
                               allocation: ComponentAllocation,
                               component: ComponentDefinition,
                               io_point: IOPoint) -> None:
        """为端子排分配端子号"""
        io_tag = getattr(io_point, 'tag', 'Unknown')

        # 简化实现：为每个I/O点分配两个连续的端子号
        # 实际实现需要管理端子号的占用情况
        base_terminal = 1  # 这里需要实际的端子号管理逻辑
        allocation.terminal_numbers = [str(base_terminal), str(base_terminal + 1)]

        self.logger.debug(f"[I/O点: {io_tag}] 端子排 {component.name} 分配端子号: {allocation.terminal_numbers}")
    
    def _assign_barrier_info(self,
                           allocation: ComponentAllocation,
                           component: ComponentDefinition,
                           io_point: IOPoint) -> None:
        """为安全栅分配信息"""
        io_tag = getattr(io_point, 'tag', 'Unknown')

        # 安全栅通常与ETP器件关联，这里可以添加相关逻辑
        if component.slots:
            allocation.slot = component.slots[0]

        self.logger.debug(f"[I/O点: {io_tag}] 安全栅 {component.name} 分配完成")

    def _calculate_component_sequence_on_rail(self, rail: RailInstance,
                                            current_allocation: ComponentAllocation) -> int:
        """
        计算器件在导轨上的序号（第几个器件）

        Args:
            rail: 导轨实例
            current_allocation: 当前分配结果

        Returns:
            器件序号（从1开始）
        """
        # 获取该导轨上所有已分配的器件（包括当前器件）
        all_allocations = rail.allocated_components

        # 按位置排序
        sorted_allocations = sorted(all_allocations, key=lambda x: x.position_start)

        # 找到当前器件的位置
        for i, allocation in enumerate(sorted_allocations):
            if allocation == current_allocation:
                return i + 1  # 序号从1开始

        # 如果没找到，返回总数+1（这种情况不应该发生）
        return len(sorted_allocations) + 1

    def _generate_component_name(self, component: ComponentDefinition,
                               actual_cabinet: ActualCabinet,
                               rail: RailInstance,
                               sequence: int,
                               io_point: IOPoint) -> str:
        """
        生成器件名称

        Args:
            component: 器件定义
            actual_cabinet: 实际机柜
            rail: 导轨实例
            sequence: 器件序号
            io_point: I/O点对象

        Returns:
            生成的器件名称
        """
        try:
            # 确定命名元素类型
            element_type = self._map_component_type_to_naming_element(component.component_type)

            # 创建命名上下文
            context = NamingContext(
                cabinet_name=actual_cabinet.name,
                rack_name="R01",  # 简化的机架名称
                slot_name="01",   # 简化的槽位名称
                signal_type=getattr(io_point, 'signal_type', None),
                sequence_number=sequence,
                custom_attributes={
                    'rail_name': rail.name,
                    'component_type': component.component_type.value,
                    'part_number': component.part_number,
                    'cabinet_type': actual_cabinet.cabinet_type,
                    'io_point_tag': getattr(io_point, 'tag', ''),
                    'rail_sequence': sequence
                }
            )

            # 生成名称
            naming_result = self.naming_engine.generate_name(element_type, context)

            if naming_result.success:
                return naming_result.generated_name
            else:
                # 如果命名失败，使用默认格式
                default_name = f"{component.component_type.value}_{actual_cabinet.name}_{rail.name}_{sequence:03d}"
                self.logger.warning(f"器件命名失败，使用默认名称: {default_name}")
                return default_name

        except Exception as e:
            # 异常情况下的默认命名
            default_name = f"{component.name}_{sequence:03d}"
            self.logger.error(f"器件命名异常，使用默认名称 {default_name}: {e}")
            return default_name

    def _find_suitable_cabinet(self, components: List[ComponentDefinition],
                              available_cabinets: List[ActualCabinet],
                              io_point: IOPoint) -> Optional[ActualCabinet]:
        """
        为器件列表找到最适合的机柜

        Args:
            components: 需要分配的器件列表
            available_cabinets: 可用机柜列表
            io_point: I/O点对象

        Returns:
            最适合的机柜，如果没有找到返回None
        """
        io_tag = getattr(io_point, 'tag', 'Unknown')

        # 按机柜类型分组
        cabinets_by_type = self._get_cabinets_by_type(available_cabinets)

        # 确定所需的机柜类型
        required_types = set()
        for component in components:
            required_type = self._determine_required_cabinet_type(component)
            required_types.add(required_type)

        self.logger.debug(f"[I/O点: {io_tag}] 器件需要的机柜类型: {required_types}")

        # 按优先级尝试机柜类型
        for required_type in ['Marshalling', 'System', 'System/Marshalling']:
            if required_type not in required_types:
                continue

            candidate_cabinets = cabinets_by_type.get(required_type, [])
            self.logger.debug(f"[I/O点: {io_tag}] 检查 {required_type} 类型机柜: {len(candidate_cabinets)} 个")

            for cabinet in candidate_cabinets:
                if self._check_cabinet_can_accommodate_all_components(components, cabinet, io_point):
                    self.logger.info(f"[I/O点: {io_tag}] 找到合适的 {required_type} 机柜: {cabinet.name}")
                    return cabinet
                else:
                    self.logger.debug(f"[I/O点: {io_tag}] 机柜 {cabinet.name} 无法容纳所有器件")

        # 尝试混合类型机柜
        mixed_cabinets = cabinets_by_type.get('System/Marshalling', [])
        self.logger.debug(f"[I/O点: {io_tag}] 检查混合类型机柜: {len(mixed_cabinets)} 个")

        for cabinet in mixed_cabinets:
            if self._check_cabinet_can_accommodate_all_components(components, cabinet, io_point):
                self.logger.info(f"[I/O点: {io_tag}] 找到合适的混合机柜: {cabinet.name}")
                return cabinet
            else:
                self.logger.debug(f"[I/O点: {io_tag}] 混合机柜 {cabinet.name} 无法容纳所有器件")

        self.logger.warning(f"[I/O点: {io_tag}] 未找到能容纳所有器件的机柜")
        return None

    def _get_cabinets_by_type(self, cabinets: List[ActualCabinet]) -> Dict[str, List[ActualCabinet]]:
        """按机柜类型分组机柜"""
        cabinets_by_type = {}

        for cabinet in cabinets:
            cabinet_type = cabinet.cabinet_type
            if cabinet_type not in cabinets_by_type:
                cabinets_by_type[cabinet_type] = []
            cabinets_by_type[cabinet_type].append(cabinet)

        return cabinets_by_type

    def _check_cabinet_can_accommodate_all_components(self, components: List[ComponentDefinition],
                                                    cabinet: ActualCabinet,
                                                    io_point: IOPoint) -> bool:
        """
        检查机柜是否能容纳所有器件

        Args:
            components: 器件列表
            cabinet: 机柜对象
            io_point: I/O点对象

        Returns:
            是否能容纳所有器件
        """
        io_tag = getattr(io_point, 'tag', 'Unknown')

        # 检查机柜类型兼容性
        incompatible_components = []
        for component in components:
            if not self._validate_component_cabinet_compatibility(component, cabinet):
                incompatible_components.append(component.name)

        if incompatible_components:
            self.logger.debug(f"[I/O点: {io_tag}] 机柜 {cabinet.name} 类型不兼容的器件: {incompatible_components}")
            return False

        # 检查导轨空间
        insufficient_space_components = []
        for component in components:
            compatible_rails = cabinet.find_compatible_rails(component)
            if not compatible_rails:
                insufficient_space_components.append(f"{component.name}(无兼容导轨)")
                continue

            # 检查是否有足够空间
            suitable_rail = self._select_best_rail(compatible_rails, component)
            if not suitable_rail:
                insufficient_space_components.append(f"{component.name}(无可用空间)")

        if insufficient_space_components:
            self.logger.debug(f"[I/O点: {io_tag}] 机柜 {cabinet.name} 空间不足的器件: {insufficient_space_components}")
            return False

        self.logger.debug(f"[I/O点: {io_tag}] 机柜 {cabinet.name} 可以容纳所有 {len(components)} 个器件")
        return True

    def _determine_required_cabinet_type(self, component: ComponentDefinition) -> str:
        """根据器件在典型回路中的位置确定所需的机柜类型"""
        # 基于器件在典型回路中的机柜位置来确定所需的机柜类型
        # 这比基于 HardwareType 更准确，因为同样的 HardwareType 可能出现在不同的机柜中

        cabinet_location = getattr(component, 'cabinet_location', 'MarshallingCabinet')

        # 机柜位置到机柜类型的映射
        location_to_cabinet_mapping = {
            'MarshallingCabinet': 'Marshalling',           # 汇控柜器件 -> 辅助柜
            'System-MarshallingCabinet': 'Marshalling',    # 系统-汇控柜器件 -> 辅助柜（也可以是混合柜）
            'SystemCabinet': 'System'                      # 系统柜器件 -> 系统柜
        }

        required_type = location_to_cabinet_mapping.get(cabinet_location, 'Marshalling')

        self.logger.debug(f"器件 {component.name} 位于 {cabinet_location}，需要 {required_type} 类型机柜")
        return required_type

    def _validate_component_cabinet_compatibility(self, component: ComponentDefinition,
                                                 actual_cabinet: ActualCabinet) -> bool:
        """验证器件与机柜类型的兼容性"""
        required_cabinet_type = self._determine_required_cabinet_type(component)
        cabinet_type = actual_cabinet.cabinet_type

        # 兼容性检查规则
        compatibility_rules = {
            'System': ['System', 'System/Marshalling'],
            'Marshalling': ['Marshalling', 'System/Marshalling'],
            'System/Marshalling': ['System', 'Marshalling', 'System/Marshalling']
        }

        compatible_types = compatibility_rules.get(required_cabinet_type, [])
        return cabinet_type in compatible_types

    def _map_component_type_to_naming_element(self, component_type: ComponentType) -> NamingElementType:
        """将器件类型映射到命名元素类型"""
        mapping = {
            ComponentType.FIELD_TERM_IN: NamingElementType.TERMINAL_BLOCK,
            ComponentType.FIELD_TERM_OUT: NamingElementType.TERMINAL_BLOCK,
            ComponentType.ETP: NamingElementType.CARD,
            ComponentType.BARRIER: NamingElementType.CARD,
            ComponentType.IO_MODULE: NamingElementType.CARD,
            ComponentType.CHASSIS: NamingElementType.RACK,
            ComponentType.CONNECTOR: NamingElementType.TERMINAL_BLOCK
        }

        return mapping.get(component_type, NamingElementType.TERMINAL_BLOCK)

    def _extract_screw_id_for_component(self, component: ComponentDefinition,
                                      io_point: IOPoint) -> str:
        """
        为器件提取ScrewID

        Args:
            component: 器件定义
            io_point: I/O点对象

        Returns:
            ScrewID字符串
        """
        io_tag = getattr(io_point, 'tag', 'Unknown')

        try:
            # 获取I/O点的典型回路
            typical_name = getattr(io_point, 'wiring_typical', None)
            if not typical_name:
                return ""

            typical_def = self.typical_manager.get_typical_by_name(typical_name)
            if not typical_def:
                return ""

            # 查找与该器件相关的Wire信息
            for wire in typical_def.wires:
                # 检查Wire的End1和End2是否包含该器件
                end1 = wire.get('end1', '')
                end2 = wire.get('end2', '')

                # 如果器件名称出现在End1或End2中
                if component.name in end1:
                    screw_id = wire.get('end1_screw_id', '')
                    if screw_id:
                        self.logger.debug(f"[I/O点: {io_tag}] 器件 {component.name} 提取ScrewID: {screw_id} (来自End1)")
                    return screw_id
                elif component.name in end2:
                    screw_id = wire.get('end2_screw_id', '')
                    if screw_id:
                        self.logger.debug(f"[I/O点: {io_tag}] 器件 {component.name} 提取ScrewID: {screw_id} (来自End2)")
                    return screw_id

            # 如果没有找到相关Wire，返回空字符串
            return ""

        except Exception as e:
            self.logger.warning(f"[I/O点: {io_tag}] 提取器件 {component.name} 的ScrewID失败: {e}")
            return ""


class ActualCabinetFactory:
    """实际机柜工厂类"""
    
    def __init__(self, cabinet_templates_directory: str = "01B_Cabinet Templates"):
        self.logger = get_logger(__name__)
        self.templates_directory = Path(cabinet_templates_directory)
    
    def create_actual_cabinet_from_pidb(self, 
                                      cabinet_data: Dict[str, Any],
                                      cabinet_templates: Dict[str, Any]) -> ActualCabinet:
        """
        从PIDB数据创建实际机柜实例
        
        Args:
            cabinet_data: PIDB中的机柜数据
            cabinet_templates: 机柜模板数据
            
        Returns:
            实际机柜实例
        """
        try:
            # 获取基本信息
            actual_name = cabinet_data.get('name', '')
            template_name = cabinet_data.get('template', '')
            location = cabinet_data.get('location', '')
            
            # 获取机柜模板
            template = cabinet_templates.get(template_name)
            if not template:
                raise ValueError(f"未找到机柜模板: {template_name}")
            
            # 创建导轨实例
            rails = self._create_rail_instances(template.get('rails', []))
            
            actual_cabinet = ActualCabinet(
                name=actual_name,
                template_name=template_name,
                location=location,
                cabinet_type=template.get('type', 'Unknown'),
                rails=rails,
                properties=cabinet_data
            )
            
            self.logger.debug(f"创建实际机柜: {actual_name} (模板: {template_name})")
            return actual_cabinet
            
        except Exception as e:
            self.logger.error(f"创建实际机柜失败: {e}")
            raise
    
    def _create_rail_instances(self, rail_templates: List[Dict[str, Any]]) -> List[RailInstance]:
        """从模板创建导轨实例"""
        rails = []
        
        for rail_template in rail_templates:
            try:
                rail = RailInstance(
                    name=rail_template.get('name', ''),
                    position=rail_template.get('position', ''),
                    length=float(rail_template.get('length', 1000)),
                    io_type=rail_template.get('io_type', 'Mixed'),
                    intrinsic=rail_template.get('intrinsic', 'NIS'),
                    voltage_level=int(rail_template.get('voltage_level', 24)),
                    supported_part_types=rail_template.get('supported_part_types', []),
                    reserved_from=float(rail_template.get('reserved_from', 0)),
                    reserved_to=float(rail_template.get('reserved_to', 0))
                )
                rails.append(rail)
                
            except Exception as e:
                self.logger.warning(f"创建导轨实例失败: {e}")
        
        return rails
