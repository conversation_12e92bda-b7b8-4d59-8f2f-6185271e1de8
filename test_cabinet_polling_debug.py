#!/usr/bin/env python3
"""
机柜轮询机制调试测试
验证智能机柜选择和轮询机制是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cabinet_polling_mechanism():
    """测试机柜轮询机制"""
    print("=== 机柜轮询机制调试测试 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        from core.data_models import IOPoint, SignalType
        
        # 创建配置
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        
        # 加载真实PIDB数据
        print("步骤1: 加载PIDB数据")
        
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        data_loader = DataLoader(config)
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        print(f"✅ 加载PIDB数据: {len(pidb_data.get('cabinets', []))} 个机柜")
        
        # 显示可用机柜
        cabinets = pidb_data.get('cabinets', [])
        print(f"\n可用机柜列表:")
        for cabinet in cabinets:
            name = cabinet.get('name', 'Unknown')
            template = cabinet.get('template', 'Unknown')
            cabinet_type = cabinet.get('type', 'Unknown')
            print(f"  - {name} (模板: {template}, 类型: {cabinet_type})")
        
        # 创建机柜配置
        cabinet_profiles = {
            'PPG SYS': {
                'type': 'System',
                'rails': [
                    {
                        'name': 'Rail_SYS_01',
                        'position': 'System',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'ETP',
                        'PartType02': 'IOModule',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG BAR': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_MAR_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG REL': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_REL_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG RIO': {
                'type': 'System/Marshalling',
                'rails': [
                    {
                        'name': 'Rail_RIO_01',
                        'position': 'Mixed',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'IOModule',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            }
        }
        
        # 创建数据模型
        print(f"\n步骤2: 创建数据模型")
        
        # 模拟问题场景中的I/O点
        iodb_data = {
            'io_points': [
                IOPoint(
                    tag="1105-FZALL-10202-1",
                    signal_type=SignalType.DI,
                    description="Fire Zone All",
                    wiring_typical="DI NIS REMOTE",  # 这个典型回路需要Marshalling机柜
                    location="Field Area 1105"
                )
            ],
            'cables': {
                'Cable_1105_FZALL': {
                    'name': 'Cable_1105_FZALL',
                    'io_points': [
                        IOPoint(
                            tag="1105-FZALL-10202-1",
                            signal_type=SignalType.DI,
                            description="Fire Zone All",
                            wiring_typical="DI NIS REMOTE",
                            location="Field Area 1105"
                        )
                    ]
                }
            }
        }
        
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            {}
        )
        
        cabinets = models.get('cabinets', [])
        cables = models.get('cables', [])
        
        print(f"数据模型创建完成: {len(cabinets)} 个机柜, {len(cables)} 条电缆")
        
        # 步骤3: 执行分配并观察轮询过程
        print(f"\n步骤3: 执行分配并观察轮询过程")
        
        allocator = IOAllocator(config)
        
        print(f"开始分配I/O点: 1105-FZALL-10202-1")
        print(f"典型回路: DI NIS REMOTE (需要Marshalling类型机柜)")
        print(f"可用机柜类型:")
        for cabinet in cabinets:
            name = cabinet.get('name', 'Unknown')
            cabinet_type = cabinet.get('type', 'Unknown')
            print(f"  - {name}: {cabinet_type}")
        
        # 执行分配
        result = allocator.allocate_io_points(cables, cabinets, {})
        
        print(f"\n步骤4: 分析分配结果")
        print(f"分配成功: {result.success}")
        print(f"成功分配: {len(result.allocated_points)}")
        print(f"失败分配: {len(result.failed_points)}")
        
        # 检查是否有智能机柜选择的日志
        cabinet_selection_found = False
        marshalling_cabinet_tried = False
        
        if result.errors:
            print(f"\n错误信息分析:")
            for error in result.errors:
                print(f"  - {error}")
                
                # 检查是否尝试了Marshalling机柜
                if any(marshalling_cabinet in error for marshalling_cabinet in ['1105-SIS-BAR-101', '1105-SIS-REL-101']):
                    marshalling_cabinet_tried = True
                    print(f"    ✅ 系统尝试了Marshalling机柜")
                elif '1105-SIS-SYS-101' in error:
                    print(f"    ⚠️  系统仍在使用System机柜")
        
        if result.warnings:
            print(f"\n警告信息分析:")
            for warning in result.warnings:
                print(f"  - {warning}")
                
                # 检查智能机柜选择日志
                if "智能选择机柜" in warning or "未找到合适的机柜" in warning:
                    cabinet_selection_found = True
                    print(f"    ✅ 发现智能机柜选择日志")
        
        # 步骤5: 评估轮询机制状态
        print(f"\n步骤5: 轮询机制状态评估")
        
        evaluation_results = [
            (cabinet_selection_found, "智能机柜选择机制被调用"),
            (marshalling_cabinet_tried, "系统尝试了Marshalling类型机柜"),
            (len(cabinets) >= 3, "有足够的机柜可供选择"),
            (any(cab.get('type') == 'Marshalling' for cab in cabinets), "存在Marshalling类型机柜")
        ]
        
        passed_checks = 0
        for passed, description in evaluation_results:
            status = "✅" if passed else "❌"
            print(f"  {status} {description}")
            if passed:
                passed_checks += 1
        
        print(f"\n轮询机制健康度: {passed_checks}/{len(evaluation_results)} ({passed_checks/len(evaluation_results)*100:.1f}%)")
        
        # 如果轮询机制有问题，提供诊断信息
        if passed_checks < len(evaluation_results):
            print(f"\n🔍 问题诊断:")
            if not cabinet_selection_found:
                print(f"  ❌ 智能机柜选择机制未被调用")
                print(f"     可能原因: available_cabinets参数未正确传递")
            if not marshalling_cabinet_tried:
                print(f"  ❌ 系统未尝试Marshalling机柜")
                print(f"     可能原因: 机柜类型识别错误或轮询逻辑有bug")
        
        return passed_checks == len(evaluation_results)
        
    except Exception as e:
        print(f"❌ 机柜轮询机制测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始机柜轮询机制调试测试...")
    
    success = test_cabinet_polling_mechanism()
    
    print(f"\n=== 测试结果总结 ===")
    
    if success:
        print("🎉 机柜轮询机制工作正常！")
    else:
        print("⚠️  机柜轮询机制存在问题，需要修复。")
        print("\n🔧 可能的修复方向:")
        print("1. 检查available_cabinets参数传递")
        print("2. 验证智能机柜选择逻辑")
        print("3. 确认机柜类型识别正确性")
        print("4. 检查机柜兼容性验证逻辑")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
