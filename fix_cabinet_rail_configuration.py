#!/usr/bin/env python3
"""
修复机柜导轨配置问题
确保所有机柜都有正确的导轨配置
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_complete_cabinet_profiles():
    """创建完整的机柜配置"""
    return {
        'PPG SYS': {
            'type': 'System',
            'rails': [
                {
                    'name': 'Rail_SYS_01',
                    'position': 'System',
                    'length': 2000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'ETP',
                    'PartType02': 'IOModule',
                    'PartType03': 'Chassis',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        },
        'PPG BAR': {
            'type': 'Marshalling',
            'rails': [
                {
                    'name': 'Rail_MAR_01',
                    'position': 'Marshalling',
                    'length': 2000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'Barrier',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        },
        'PPG REL': {
            'type': 'Marshalling',
            'rails': [
                {
                    'name': 'Rail_REL_01',
                    'position': 'Marshalling',
                    'length': 2000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'Barrier',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        },
        'PPG RIO': {
            'type': 'System/Marshalling',
            'rails': [
                {
                    'name': 'Rail_RIO_01',
                    'position': 'Mixed',
                    'length': 2000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'IOModule',
                    'PartType05': 'Chassis',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        }
    }

def test_fixed_cabinet_configuration():
    """测试修复后的机柜配置"""
    print("=== 测试修复后的机柜配置 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        from core.data_models import IOPoint, SignalType
        
        # 创建配置
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        
        # 加载PIDB数据
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        data_loader = DataLoader(config)
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        # 使用修复后的机柜配置
        cabinet_profiles = create_complete_cabinet_profiles()
        
        # 创建测试I/O点
        iodb_data = {
            'io_points': [
                IOPoint(
                    tag="1105-HZ-004-3",
                    signal_type=SignalType.DI,
                    description="Test DI Point",
                    wiring_typical="DI NIS N",
                    location="Field Area 1105"
                )
            ],
            'cables': {
                'Cable_Test': {
                    'name': 'Cable_Test',
                    'io_points': [
                        IOPoint(
                            tag="1105-HZ-004-3",
                            signal_type=SignalType.DI,
                            description="Test DI Point",
                            wiring_typical="DI NIS N",
                            location="Field Area 1105"
                        )
                    ]
                }
            }
        }
        
        # 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            {}
        )
        
        cabinets = models.get('cabinets', [])
        cables = models.get('cables', [])
        
        print(f"修复后的机柜配置:")
        print(f"  可用机柜: {len(cabinets)} 个")
        
        all_cabinets_have_rails = True
        
        for cabinet in cabinets:
            name = cabinet.get('name', 'Unknown')
            cabinet_type = cabinet.get('type', 'Unknown')
            template = cabinet.get('template', 'Unknown')
            rails = cabinet.get('rails', [])
            
            print(f"    - {name} ({cabinet_type}, 模板: {template}): {len(rails)} 个导轨")
            
            if len(rails) == 0:
                all_cabinets_have_rails = False
                print(f"      ❌ 机柜没有导轨配置")
            else:
                for rail in rails:
                    rail_name = rail.get('name', 'Unknown')
                    supported_types = []
                    for i in range(1, 11):
                        part_type_key = f'PartType{i:02d}'
                        part_type_value = rail.get(part_type_key)
                        if part_type_value:
                            supported_types.append(part_type_value)
                    print(f"      ✅ 导轨 {rail_name}: {supported_types}")
        
        if not all_cabinets_have_rails:
            print("❌ 仍有机柜缺少导轨配置")
            return False
        
        # 执行分配测试
        print(f"\n执行分配测试:")
        allocator = IOAllocator(config)
        result = allocator.allocate_io_points(cables, cabinets, {})
        
        print(f"  分配成功: {result.success}")
        print(f"  成功分配: {len(result.allocated_points)}")
        print(f"  失败分配: {len(result.failed_points)}")
        
        if result.errors:
            print(f"  错误信息:")
            for error in result.errors:
                print(f"    - {error}")
        
        if result.allocated_points:
            print(f"  成功分配的I/O点:")
            for io_point in result.allocated_points:
                cabinet_name = getattr(io_point, 'cabinet', 'Unknown')
                print(f"    - {io_point.tag} -> {cabinet_name}")
        
        return all_cabinets_have_rails and result.success
        
    except Exception as e:
        print(f"❌ 机柜配置修复测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chassis_component_handling():
    """测试Chassis器件处理"""
    print("\n=== 测试Chassis器件处理 ===")
    
    try:
        from core.component_allocation_engine import ComponentAllocationEngine
        from core.component_allocation_models import ComponentDefinition, ComponentType, ActualCabinet, RailInstance
        from core.data_models import IOPoint, SignalType
        
        # 创建分配引擎
        engine = ComponentAllocationEngine("01C_Wiring Typical")
        
        # 创建支持Chassis的机柜
        rail = RailInstance(
            name="Rail_RIO_01",
            position="Mixed",
            length=2000.0,
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "IOModule", "Chassis"],
            reserved_from=0.0,
            reserved_to=0.0
        )
        
        cabinet = ActualCabinet(
            name="1105-SIS-RIO-001",
            template_name="PPG RIO",
            location="Field Area 1105",
            cabinet_type="System/Marshalling",
            rails=[rail]
        )
        
        # 创建测试I/O点（包含Chassis器件）
        io_point = IOPoint(
            tag="1105-TEST-CHASSIS",
            signal_type=SignalType.DI,
            description="Test Chassis Point",
            wiring_typical="DI NIS REMOTE"  # 包含TCN_RXM_Chassis
        )
        
        print(f"测试场景:")
        print(f"  I/O点: {io_point.tag}")
        print(f"  典型回路: {io_point.wiring_typical} (包含Chassis器件)")
        print(f"  机柜: {cabinet.name} ({cabinet.cabinet_type})")
        print(f"  导轨: {rail.name} (支持: {rail.supported_part_types})")
        
        # 执行分配
        result = engine.allocate_io_point_components(io_point, cabinet, {})
        
        print(f"\n分配结果:")
        print(f"  成功: {result.success}")
        print(f"  分配的器件数: {len(result.component_allocations)}")
        print(f"  错误数: {len(result.errors)}")
        
        if result.errors:
            print(f"  错误信息:")
            for error in result.errors:
                print(f"    - {error}")
        
        if result.component_allocations:
            print(f"  成功分配的器件:")
            for alloc in result.component_allocations:
                comp_name = alloc.component_definition.name
                comp_type = alloc.component_definition.component_type.value
                rail_name = alloc.rail_name
                space_req = alloc.component_definition.space_requirement
                print(f"    - {comp_name} ({comp_type}, {space_req}mm) -> {rail_name}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ Chassis器件处理测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始机柜导轨配置修复...")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_fixed_cabinet_configuration())
    test_results.append(test_chassis_component_handling())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 修复结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("🎉 机柜导轨配置修复成功！")
        print("\n🔧 修复内容:")
        print("✅ 1. 为所有机柜模板添加了完整的导轨配置")
        print("✅ 2. 确保每个导轨都有正确的PartType设置")
        print("✅ 3. 支持Chassis器件的分配")
        print("✅ 4. 机柜轮询机制现在可以使用所有机柜")
        
        print("\n📋 机柜配置详情:")
        print("- PPG SYS: 支持 ETP, IOModule, Chassis")
        print("- PPG BAR: 支持 FieldTermIn, FieldTermOut, ETP, Barrier")
        print("- PPG REL: 支持 FieldTermIn, FieldTermOut, ETP, Barrier")
        print("- PPG RIO: 支持 FieldTermIn, FieldTermOut, ETP, IOModule, Chassis")
        
        return True
    else:
        print("⚠️  机柜导轨配置修复部分成功，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
