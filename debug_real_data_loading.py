#!/usr/bin/env python3
"""
真实数据加载调试脚本
用于验证从PIDB文件加载的机柜配置是否正确设置了supported_part_types
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import logging
from core.logger import setup_logger, get_logger
from core.allocator import IOAllocator
from core.pidb_cabinet_loader import PIDBCabinetLoader
from core.data_models import IOPoint, SignalType


def setup_debug_logging():
    """设置调试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('debug_real_data_loading.log', mode='w', encoding='utf-8')
        ]
    )


def test_pidb_cabinet_loading():
    """测试PIDB机柜数据加载"""
    print("=== 测试PIDB机柜数据加载 ===")
    
    try:
        # 查找PIDB文件
        pidb_files = list(Path("data/pidb").glob("*.xlsx"))
        if not pidb_files:
            print("❌ 未找到PIDB文件")
            return False
        
        pidb_file = pidb_files[0]
        print(f"📁 使用PIDB文件: {pidb_file}")
        
        # 直接读取Excel文件
        import pandas as pd
        df = pd.read_excel(pidb_file, sheet_name='cabinet')
        cabinet_data = df.to_dict('records')
        
        if not cabinet_data:
            print("❌ PIDB机柜数据加载失败")
            return False
        
        print(f"✅ 成功加载 {len(cabinet_data)} 个机柜")
        
        # 转换PIDB数据格式
        converted_cabinets = []
        for cabinet_row in cabinet_data:
            converted_cabinet = {
                'name': cabinet_row.get('Cabinet', ''),
                'template': cabinet_row.get('CabinetTemplate', ''),
                'type': 'Marshalling',  # 默认类型
                'location': 'Unknown',  # 默认位置
                'cabinet_no': cabinet_row.get('CabinetNo', 0)
            }
            converted_cabinets.append(converted_cabinet)

        # 查找目标机柜
        target_cabinet = None
        for cabinet in converted_cabinets:
            if cabinet.get('name') == '1105-SIS-REL-101':
                target_cabinet = cabinet
                break

        if not target_cabinet:
            print("❌ 未找到目标机柜 1105-SIS-REL-101")
            print("可用机柜:")
            for cabinet in converted_cabinets[:5]:  # 只显示前5个
                print(f"  - {cabinet.get('name', 'Unknown')}")
            return False

        print(f"✅ 找到目标机柜: {target_cabinet['name']}")
        print(f"   模板: {target_cabinet.get('template', 'Unknown')}")
        print(f"   类型: {target_cabinet.get('type', 'Unknown')}")
        print(f"   位置: {target_cabinet.get('location', 'Unknown')}")
        
        return target_cabinet
        
    except Exception as e:
        print(f"❌ PIDB数据加载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_cabinet_template_loading(cabinet_data):
    """测试机柜模板加载 - 跳过此步骤，直接使用原始数据"""
    print("\n=== 跳过机柜模板加载，直接使用PIDB数据 ===")

    # 为机柜数据添加必要的导轨配置（模拟机柜模板数据）
    enriched_cabinet = cabinet_data.copy()

    # 根据机柜模板添加导轨配置
    template_name = enriched_cabinet.get('template', '')
    if 'REL' in template_name:
        # REL机柜通常有汇控导轨
        enriched_cabinet['rails'] = [
            {
                'name': 'Rail_REL_01',
                'position': 'Marshalling',
                'length': 2000.0,
                'io_type': 'Mixed',
                'intrinsic': 'NIS',
                'voltage_level': 24,
                'PartType01': 'FieldTermIn',
                'PartType02': 'FieldTermOut',
                'PartType03': 'ETP',
                'PartType04': 'Barrier',
                'reserved_from': 0.0,
                'reserved_to': 0.0
            }
        ]
    else:
        # 其他机柜类型的默认配置
        enriched_cabinet['rails'] = [
            {
                'name': 'Rail_Default_01',
                'position': 'Default',
                'length': 1000.0,
                'io_type': 'Mixed',
                'intrinsic': 'NIS',
                'voltage_level': 24,
                'PartType01': 'FieldTermIn',
                'PartType02': 'FieldTermOut',
                'reserved_from': 0.0,
                'reserved_to': 0.0
            }
        ]

    print(f"✅ 机柜数据准备完成")
    print(f"   机柜名称: {enriched_cabinet.get('name')}")
    print(f"   导轨数量: {len(enriched_cabinet.get('rails', []))}")

    # 检查导轨配置
    rails = enriched_cabinet.get('rails', [])
    print(f"\n📏 导轨配置详情:")
    for i, rail in enumerate(rails, 1):
        print(f"  导轨 {i}: {rail.get('name', 'Unknown')}")
        print(f"    位置: {rail.get('position', 'Unknown')}")
        print(f"    长度: {rail.get('length', 0)}mm")
        print(f"    I/O类型: {rail.get('io_type', 'Unknown')}")
        print(f"    本安类型: {rail.get('intrinsic', 'Unknown')}")

        # 检查PartType配置
        part_types = []
        for j in range(1, 11):  # 检查PartType01到PartType10
            part_type_key = f'PartType{j:02d}'
            if part_type_key in rail and rail[part_type_key]:
                part_types.append(rail[part_type_key])

        print(f"    支持的器件类型: {part_types}")

    return enriched_cabinet


def test_cabinet_conversion(enriched_cabinet):
    """测试机柜字典转换为ActualCabinet对象"""
    print("\n=== 测试机柜字典转换 ===")
    
    try:
        # 创建分配器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        allocator = IOAllocator(config)
        
        # 转换机柜字典
        actual_cabinet = allocator._convert_dict_to_actual_cabinet(enriched_cabinet)
        
        if not actual_cabinet:
            print("❌ 机柜字典转换失败")
            return False
        
        print(f"✅ 机柜转换成功")
        print(f"   机柜名称: {actual_cabinet.name}")
        print(f"   机柜类型: {actual_cabinet.cabinet_type}")
        print(f"   导轨数量: {len(actual_cabinet.rails)}")
        
        # 检查导轨的supported_part_types
        print(f"\n🔧 导轨兼容性检查:")
        for rail in actual_cabinet.rails:
            print(f"  导轨: {rail.name}")
            print(f"    位置: {rail.position}")
            print(f"    长度: {rail.length}mm")
            print(f"    可用长度: {rail.available_length}mm")
            print(f"    支持的器件类型: {rail.supported_part_types}")
            
            if not rail.supported_part_types:
                print(f"    ❌ 错误: 导轨 {rail.name} 的 supported_part_types 为空")
            else:
                print(f"    ✅ 导轨 {rail.name} 支持 {len(rail.supported_part_types)} 种器件类型")
        
        return actual_cabinet
        
    except Exception as e:
        print(f"❌ 机柜转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始真实数据加载调试...")
    
    # 设置调试日志
    setup_debug_logging()
    
    test_results = []
    
    # 1. 测试PIDB数据加载
    cabinet_data = test_pidb_cabinet_loading()
    test_results.append(cabinet_data is not False)
    
    if cabinet_data:
        # 2. 测试机柜模板加载
        enriched_cabinet = test_cabinet_template_loading(cabinet_data)
        test_results.append(enriched_cabinet is not False)
        
        if enriched_cabinet:
            # 3. 测试机柜转换
            actual_cabinet = test_cabinet_conversion(enriched_cabinet)
            test_results.append(actual_cabinet is not False)
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 调试结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过，真实数据加载正常")
    else:
        print("⚠️  部分测试失败，需要进一步检查数据配置")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
