#!/usr/bin/env python3
"""
真实机柜模板解析测试
验证从XML文件中正确解析13根导轨
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ppg_bar_template_parsing():
    """测试PPG BAR模板解析"""
    print("=== 测试PPG BAR模板解析 ===")
    
    try:
        from core.cabinet_template_parser import CabinetTemplateParser
        
        parser = CabinetTemplateParser()
        
        # 解析PPG BAR模板
        ppg_bar_config = parser.parse_cabinet_template("PPG BAR")
        
        if not ppg_bar_config:
            print("❌ PPG BAR模板解析失败")
            return False
        
        print(f"✅ PPG BAR模板解析成功")
        print(f"  机柜类型: {ppg_bar_config.get('type', 'Unknown')}")
        print(f"  导轨数量: {len(ppg_bar_config.get('rails', []))}")
        
        rails = ppg_bar_config.get('rails', [])
        
        # 验证导轨数量
        if len(rails) != 13:
            print(f"❌ 导轨数量不正确，期望13根，实际{len(rails)}根")
            return False
        
        print(f"✅ 导轨数量正确: 13根")
        
        # 验证关键导轨
        expected_rails = [
            'Rail_A', 'Rail_F', 'Rail_G', 'Rail_B2', 'Rail_C2',
            'Rail_TS01', 'Rail_TD01', 'Rail_TSF01', 'Rail_TDF01',
            'Rail_B1', 'Rail_C1', 'Rail_XX', 'Rail_ISL'
        ]
        
        actual_rail_names = [rail['name'] for rail in rails]
        
        missing_rails = []
        for expected_rail in expected_rails:
            if expected_rail not in actual_rail_names:
                missing_rails.append(expected_rail)
        
        if missing_rails:
            print(f"❌ 缺少导轨: {missing_rails}")
            return False
        
        print(f"✅ 所有预期导轨都存在")
        
        # 详细显示每根导轨
        print(f"\n导轨详细信息:")
        for i, rail in enumerate(rails, 1):
            name = rail['name']
            position = rail['position']
            length = rail['length']
            io_type = rail['io_type']
            intrinsic = rail['intrinsic']
            
            # 获取支持的器件类型
            part_types = []
            for j in range(1, 11):
                part_type_key = f'PartType{j:02d}'
                if part_type_key in rail:
                    part_types.append(rail[part_type_key])
            
            print(f"  {i:2d}. {name} ({position})")
            print(f"      长度: {length}mm, I/O: {io_type}, 本安: {intrinsic}")
            print(f"      支持器件: {part_types}")
        
        return True
        
    except Exception as e:
        print(f"❌ PPG BAR模板解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_data_loader_with_real_templates():
    """测试数据加载器使用真实模板"""
    print("\n=== 测试数据加载器使用真实模板 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        from core.data_models import IOPoint, SignalType
        
        # 创建数据加载器
        data_loader = DataLoader({})
        
        # 加载PIDB数据
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        # 创建测试数据
        iodb_data = {
            'io_points': [
                IOPoint(
                    tag="1105-TEST-001",
                    signal_type=SignalType.DI,
                    description="Test Point",
                    wiring_typical="DI NIS N",
                    location="Field Area 1105"
                )
            ],
            'cables': {
                'Cable_Test': {
                    'name': 'Cable_Test',
                    'io_points': [
                        IOPoint(
                            tag="1105-TEST-001",
                            signal_type=SignalType.DI,
                            description="Test Point",
                            wiring_typical="DI NIS N",
                            location="Field Area 1105"
                        )
                    ]
                }
            }
        }
        
        # 不提供cabinet_profiles，让系统使用模板解析器
        cabinet_profiles = {}
        wiring_typicals = {}
        
        print(f"测试场景: 不提供cabinet_profiles，让系统使用模板解析器")
        
        # 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            wiring_typicals
        )
        
        cabinets = models.get('cabinets', [])
        
        print(f"✅ 数据模型创建成功")
        print(f"  创建的机柜数量: {len(cabinets)}")
        
        # 检查PPG BAR机柜的导轨数量
        ppg_bar_cabinet = None
        for cabinet in cabinets:
            template = cabinet.get('template', '')
            if template == 'PPG BAR':
                ppg_bar_cabinet = cabinet
                break
        
        if ppg_bar_cabinet:
            rails = ppg_bar_cabinet.get('rails', [])
            print(f"  PPG BAR机柜导轨数量: {len(rails)}")
            
            if len(rails) == 13:
                print(f"  ✅ PPG BAR机柜导轨数量正确: 13根")
                
                # 显示前几根导轨
                print(f"  前5根导轨:")
                for i, rail in enumerate(rails[:5], 1):
                    name = rail['name']
                    length = rail['length']
                    part_types = []
                    for j in range(1, 11):
                        part_type_key = f'PartType{j:02d}'
                        if part_type_key in rail:
                            part_types.append(rail[part_type_key])
                    print(f"    {i}. {name}: {length}mm, 支持{part_types}")
                
                return True
            else:
                print(f"  ❌ PPG BAR机柜导轨数量不正确，期望13根，实际{len(rails)}根")
                return False
        else:
            print(f"  ❌ 未找到PPG BAR机柜")
            return False
        
    except Exception as e:
        print(f"❌ 数据加载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_all_cabinet_templates():
    """测试所有机柜模板"""
    print("\n=== 测试所有机柜模板 ===")
    
    try:
        from core.cabinet_template_parser import CabinetTemplateParser
        
        parser = CabinetTemplateParser()
        
        # 加载所有模板
        all_templates = parser.load_all_cabinet_templates()
        
        print(f"✅ 加载了 {len(all_templates)} 个机柜模板")
        
        for template_name, template_config in all_templates.items():
            rails = template_config.get('rails', [])
            cabinet_type = template_config.get('type', 'Unknown')
            print(f"  - {template_name}: {cabinet_type}, {len(rails)} 根导轨")
        
        # 验证关键模板存在
        expected_templates = ['PPG BAR', 'PPG SYS', 'PPG REL', 'PPG RIO']
        missing_templates = []
        
        for expected in expected_templates:
            if expected not in all_templates:
                missing_templates.append(expected)
        
        if missing_templates:
            print(f"❌ 缺少模板: {missing_templates}")
            return False
        
        print(f"✅ 所有预期模板都存在")
        return True
        
    except Exception as e:
        print(f"❌ 所有机柜模板测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始真实机柜模板解析测试...")
    print("验证问题：PPG BAR机柜应该有13根导轨，而不是1根")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_ppg_bar_template_parsing())
    test_results.append(test_data_loader_with_real_templates())
    test_results.append(test_all_cabinet_templates())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 真实机柜模板解析测试结果 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 真实机柜模板解析完全成功！")
        print("\n🔧 修复总结:")
        print("✅ 1. 创建了机柜模板解析器")
        print("   - 从XML文件中正确解析所有导轨配置")
        print("   - PPG BAR机柜现在有13根导轨而不是1根")
        print("✅ 2. 修改了数据加载器")
        print("   - 优先使用传入的cabinet_profiles")
        print("   - 回退到模板解析器加载真实配置")
        print("   - 支持从XML文件动态加载所有模板")
        print("✅ 3. 完整的导轨配置")
        print("   - 每根导轨都有正确的长度、类型和支持的器件")
        print("   - 支持不同类型的器件：FieldTermIn/Out, ETP, PwrTerm, Barrier")
        
        print("\n📋 修复效果:")
        print("现在系统会：")
        print("✅ 从XML文件中读取真实的机柜配置")
        print("✅ 正确识别所有13根导轨")
        print("✅ 为每根导轨配置正确的器件类型支持")
        print("✅ 提供更准确的导轨长度和空间信息")
        
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
