"""
命名配置功能演示脚本
展示自定义命名规则的完整功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def demo_naming_engine():
    """演示命名引擎功能"""
    print("=== 命名引擎功能演示 ===\n")
    
    from core.naming_engine import NamingEngine, NamingContext
    from core.data_models import IOPoint, SignalType
    
    # 创建命名引擎
    engine = NamingEngine()
    print("1. 创建命名引擎")
    
    # 创建测试IO点
    io_point = IOPoint(
        tag="AI_TEMP_001",
        signal_type=SignalType.AI,
        description="温度传感器",
        cable_name="CABLE_001",
        pair_number="01",
        is_intrinsic=False,
        system="DCS",
        location="FIELD",
        cable_type="CABLE",
        wiring_typical="AI_TYPICAL"
    )
    
    # 设置分配信息
    io_point.allocated_rack = "Rack_1"
    io_point.allocated_slot = 3
    io_point.allocated_channel = 5
    io_point.allocated_cabinet = "1103-SIS-SYS-101"
    io_point.allocated_rail = "Rail_F"
    
    print(f"2. 创建测试IO点: {io_point.tag}")
    
    # 创建命名上下文
    additional_data = {
        'cabinet_numbers': {'1103-SIS-SYS-101': '02'},
        'card_info': {'part_number': '3721'}
    }
    context = NamingContext(io_point, additional_data)
    print("3. 创建命名上下文")
    
    # 演示各种器件命名
    print("\n4. 默认命名规则演示:")
    device_types = [
        ('barrier', '安全栅'),
        ('relay', '继电器'),
        ('isolator', '隔离器'),
        ('surge_protector', '防雷栅'),
        ('terminal_block', '端子排'),
        ('tr_terminal', 'TR端子排')
    ]
    
    for device_type, display_name in device_types:
        name = engine.generate_name(device_type, context)
        print(f"   {display_name}: {name}")
    
    print()

def demo_custom_naming_rules():
    """演示自定义命名规则"""
    print("=== 自定义命名规则演示 ===\n")
    
    from core.naming_engine import NamingEngine, NamingRule, NamingElement, NamingElementType
    
    # 创建命名引擎
    engine = NamingEngine()
    
    # 创建自定义安全栅命名规则
    custom_barrier_rule = NamingRule(
        name="自定义安全栅命名",
        device_type="barrier",
        elements=[
            NamingElement(NamingElementType.FIXED_TEXT, value="BARRIER"),
            NamingElement(NamingElementType.RACK_NUMBER),
            NamingElement(NamingElementType.SLOT_NUMBER),
            NamingElement(NamingElementType.CHANNEL_NUMBER, format_spec="02d")
        ],
        separator="_"
    )
    
    # 添加自定义规则
    engine.add_rule(custom_barrier_rule)
    print("1. 添加自定义安全栅命名规则")
    
    # 创建测试上下文
    from core.data_models import IOPoint, SignalType
    
    io_point = IOPoint(
        tag="AI_PRESSURE_002",
        signal_type=SignalType.AI,
        description="压力传感器",
        cable_name="CABLE_002",
        pair_number="02",
        is_intrinsic=False,
        system="DCS",
        location="FIELD",
        cable_type="CABLE",
        wiring_typical="AI_TYPICAL"
    )
    
    io_point.allocated_rack = "Rack_2"
    io_point.allocated_slot = 5
    io_point.allocated_channel = 8
    
    from core.naming_engine import NamingContext
    context = NamingContext(io_point, {})
    
    # 比较默认规则和自定义规则
    default_name = engine.generate_name('barrier', context)
    custom_name = engine.generate_name('barrier', context, rule_name="自定义安全栅命名")
    
    print(f"2. 默认规则生成: {default_name}")
    print(f"3. 自定义规则生成: {custom_name}")
    print()

def demo_config_management():
    """演示配置管理功能"""
    print("=== 配置管理功能演示 ===\n")
    
    import tempfile
    import json
    
    # 创建示例配置
    sample_config = {
        'barrier': {
            'prefix': 'SAFETY_',
            'separator': '-',
            'suffix': '_BARRIER',
            'elements': ['rack_number', 'slot_number', 'channel_number']
        },
        'relay': {
            'prefix': 'RELAY_',
            'separator': '_',
            'suffix': '',
            'elements': ['rack_number', 'slot_number', 'channel_number']
        },
        'terminal_block': {
            'prefix': 'TB',
            'separator': '',
            'suffix': '',
            'elements': ['cabinet_number', 'rail_number', 'device_number']
        }
    }
    
    print("1. 创建示例配置")
    for device_type, config in sample_config.items():
        print(f"   {device_type}: 前缀='{config['prefix']}', 分隔符='{config['separator']}', 后缀='{config['suffix']}'")
    
    # 保存配置到临时文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
        json.dump(sample_config, f, ensure_ascii=False, indent=2)
        temp_file = f.name
    
    print(f"\n2. 配置保存到: {temp_file}")
    
    # 加载配置
    with open(temp_file, 'r', encoding='utf-8') as f:
        loaded_config = json.load(f)
    
    print("3. 配置加载成功")
    
    # 验证配置
    if loaded_config == sample_config:
        print("4. 配置验证通过")
    else:
        print("4. 配置验证失败")
    
    # 清理临时文件
    os.unlink(temp_file)
    print("5. 临时文件清理完成")
    print()

def demo_gui_integration():
    """演示GUI集成功能"""
    print("=== GUI集成功能演示 ===\n")
    
    # 检查GUI组件
    try:
        from gui.allocation_widget import AllocationWidget
        print("1. ✓ AllocationWidget导入成功")
        
        # 检查新增的方法
        methods = [
            '_configure_naming_rules',
            '_load_naming_config',
            '_save_naming_config',
            '_export_io_report'
        ]
        
        for method in methods:
            if hasattr(AllocationWidget, method):
                print(f"2. ✓ 方法 {method} 已添加")
            else:
                print(f"2. ✗ 方法 {method} 缺失")
        
    except Exception as e:
        print(f"1. ✗ AllocationWidget导入失败: {e}")
    
    # 检查命名配置对话框
    try:
        from gui.naming_config_dialog import NamingConfigDialog
        print("3. ✓ NamingConfigDialog导入成功")
        
        # 检查对话框的关键方法
        dialog_methods = [
            '_generate_example',
            '_update_rule_preview',
            '_collect_config',
            '_load_current_config'
        ]
        
        for method in dialog_methods:
            if hasattr(NamingConfigDialog, method):
                print(f"4. ✓ 对话框方法 {method} 已实现")
            else:
                print(f"4. ✗ 对话框方法 {method} 缺失")
        
    except Exception as e:
        print(f"3. ✗ NamingConfigDialog导入失败: {e}")
    
    print()

def main():
    """主演示函数"""
    print("命名配置功能完整演示\n")
    print("=" * 50)
    
    # 运行各个演示
    demo_naming_engine()
    demo_custom_naming_rules()
    demo_config_management()
    demo_gui_integration()
    
    print("=" * 50)
    print("演示完成！")
    print("\n功能总结:")
    print("1. ✓ 灵活的命名引擎，支持多种命名元素")
    print("2. ✓ 自定义命名规则，支持前缀、分隔符、后缀")
    print("3. ✓ 配置持久化，支持保存和加载用户设置")
    print("4. ✓ GUI集成，用户友好的配置界面")
    print("5. ✓ 实时预览，所见即所得的配置体验")
    print("6. ✓ 多器件类型支持，覆盖所有常用器件")
    
    print("\n使用方式:")
    print("1. 在分配界面点击'命名规则'按钮")
    print("2. 在弹出的对话框中配置各器件的命名规则")
    print("3. 使用预览功能查看命名效果")
    print("4. 保存配置后，导出IO分配表时将使用自定义规则")

if __name__ == '__main__':
    main()
