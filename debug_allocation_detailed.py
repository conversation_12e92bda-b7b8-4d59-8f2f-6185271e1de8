#!/usr/bin/env python3
"""
详细调试I/O点分配问题
重点分析分配统计数据的计算逻辑
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_allocation_statistics():
    """详细调试分配统计数据"""
    print("=== 详细调试I/O点分配统计 ===")
    
    try:
        # 1. 加载配置和数据
        from utils.config_manager_simple import ConfigManager
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        data_loader = DataLoader(config)
        
        # 加载所有数据
        cabinet_profiles = data_loader.load_cabinet_profiles()
        wiring_typicals = data_loader.load_wiring_typicals()
        
        # 加载IODB数据
        iodb_path = Path(config_manager.get('data_paths.iodb', ''))
        iodb_files = list(iodb_path.glob("*.xlsx"))
        if not iodb_files:
            print("❌ 未找到IODB文件")
            return False
        
        iodb_data = data_loader.load_iodb_data(str(iodb_files[0]))
        
        # 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            {},
            cabinet_profiles,
            wiring_typicals
        )
        
        print(f"✓ 数据加载完成:")
        print(f"  - 原始I/O点数: {len(models['io_points'])}")
        print(f"  - 电缆数: {len(models['cables'])}")
        print(f"  - 机柜数: {len(models['cabinets'])}")
        
        # 2. 分析电缆结构
        print(f"\n=== 电缆结构分析 ===")
        total_cable_points = 0
        for i, cable in enumerate(models['cables'][:5]):  # 显示前5条电缆
            io_count = len(cable.get('io_points', []))
            total_cable_points += io_count
            print(f"  电缆{i+1}: {cable['name']} - {io_count}个I/O点")
        
        print(f"  前5条电缆总I/O点数: {total_cable_points}")
        
        # 计算所有电缆的I/O点总数
        all_cable_points = 0
        for cable in models['cables']:
            all_cable_points += len(cable.get('io_points', []))
        print(f"  所有电缆I/O点总数: {all_cable_points}")
        
        # 3. 执行分配并详细分析
        print(f"\n=== 分配过程详细分析 ===")
        allocator = IOAllocator(config)
        
        # 在分配前检查spare设置
        spare_enabled = config_manager.get('spare_settings.enable_cable_spare', True)
        print(f"  Spare点生成启用: {spare_enabled}")
        
        allocation_result = allocator.allocate_io_points(
            models['cables'],
            models['cabinets'],
            {wt.name: {'components': []} for wt in models['wiring_typicals']}
        )
        
        print(f"✓ 分配结果:")
        print(f"  - 分配成功点数: {len(allocation_result.allocated_points)}")
        print(f"  - 分配失败点数: {len(allocation_result.failed_points)}")
        print(f"  - 错误数量: {len(allocation_result.errors)}")
        print(f"  - 警告数量: {len(allocation_result.warnings)}")
        
        if allocation_result.summary:
            summary = allocation_result.summary
            print(f"  - 统计中的总点数: {summary.get('total_points', 0)}")
            print(f"  - 统计中的分配成功: {summary.get('allocated_count', 0)}")
            print(f"  - 统计中的分配失败: {summary.get('failed_count', 0)}")
            print(f"  - 成功率: {summary.get('success_rate', 0):.1f}%")
            print(f"  - Spare点数量: {summary.get('spare_points_generated', 0)}")
        
        # 4. 分析分配点的类型
        print(f"\n=== 分配点类型分析 ===")
        regular_points = 0
        spare_points = 0
        
        for point in allocation_result.allocated_points:
            if hasattr(point, 'is_spare') and point.is_spare:
                spare_points += 1
            else:
                regular_points += 1
        
        print(f"  - 常规I/O点: {regular_points}")
        print(f"  - Spare点: {spare_points}")
        print(f"  - 总分配点数: {regular_points + spare_points}")
        
        # 5. 验证数据一致性
        print(f"\n=== 数据一致性验证 ===")
        expected_regular = len(models['io_points'])
        actual_regular = regular_points
        
        print(f"  - 期望常规点数: {expected_regular}")
        print(f"  - 实际常规点数: {actual_regular}")
        print(f"  - 数据一致性: {'✓' if expected_regular == actual_regular else '❌'}")
        
        # 6. 分析成功率计算问题
        print(f"\n=== 成功率计算分析 ===")
        if allocation_result.summary:
            summary = allocation_result.summary
            total_in_summary = summary.get('total_points', 0)
            allocated_in_summary = summary.get('allocated_count', 0)
            
            print(f"  - 统计中使用的总点数: {total_in_summary}")
            print(f"  - 统计中使用的分配点数: {allocated_in_summary}")
            
            if total_in_summary > 0:
                calculated_rate = (allocated_in_summary / total_in_summary) * 100
                print(f"  - 重新计算的成功率: {calculated_rate:.1f}%")
                
                if allocated_in_summary > total_in_summary:
                    print(f"  ⚠️ 问题发现: 分配点数({allocated_in_summary}) > 总点数({total_in_summary})")
                    print(f"     这表明spare点被错误地计入了分配统计")
        
        # 7. 检查GUI显示的问题
        print(f"\n=== GUI显示问题分析 ===")
        # 模拟GUI中的统计计算
        gui_allocated = len(allocation_result.allocated_points)
        gui_failed = len(allocation_result.failed_points)
        gui_total = gui_allocated + gui_failed
        
        if gui_total > 0:
            gui_success_rate = (gui_allocated / gui_total) * 100
        else:
            gui_success_rate = 0
        
        print(f"  - GUI看到的分配成功: {gui_allocated}")
        print(f"  - GUI看到的分配失败: {gui_failed}")
        print(f"  - GUI计算的总数: {gui_total}")
        print(f"  - GUI计算的成功率: {gui_success_rate:.1f}%")
        
        if gui_success_rate > 100:
            print(f"  ⚠️ GUI成功率异常: {gui_success_rate:.1f}% > 100%")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_allocation_statistics()
    if success:
        print("\n🎉 详细调试完成!")
    else:
        print("\n❌ 调试过程中发现问题。")
