"""
EWReborn 测试套件

这个包包含了EWReborn I/O点分配系统的所有测试脚本、演示脚本和调试工具。

测试分类：
- 单元测试：测试单个模块和功能
- 集成测试：测试模块间的协作
- GUI测试：测试用户界面组件
- 演示脚本：功能演示和使用示例
- 调试工具：开发和调试辅助脚本

使用方法：
1. 运行单个测试：python tests/test_modules.py
2. 运行完整测试：python tests/test_complete_system.py
3. 查看演示：python tests/demo_file_dialog_defaults.py

注意：运行测试前请确保已安装所有依赖项：pip install -r requirements.txt
"""

__version__ = "1.0.0"
__author__ = "EWReborn Development Team"
