#!/usr/bin/env python3
"""
简化的机柜选择测试
验证智能机柜选择逻辑
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_cabinet_selection_logic():
    """测试机柜选择逻辑"""
    print("=== 简化的机柜选择测试 ===")
    
    try:
        from core.allocator import IOAllocator
        
        # 创建分配器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        allocator = IOAllocator(config)
        
        # 创建测试机柜数据
        cabinets = [
            {
                'name': '1105-SIS-SYS-101',
                'type': 'System',
                'template': 'PPG SYS',
                'rails': [{'name': 'Rail_SYS_01'}]
            },
            {
                'name': '1105-SIS-BAR-101',
                'type': 'Marshalling',
                'template': 'PPG BAR',
                'rails': [{'name': 'Rail_MAR_01'}]
            },
            {
                'name': '1105-SIS-REL-101',
                'type': 'Marshalling',
                'template': 'PPG REL',
                'rails': [{'name': 'Rail_REL_01'}]
            },
            {
                'name': '1105-SIS-RIO-001',
                'type': 'System/Marshalling',
                'template': 'PPG RIO',
                'rails': [{'name': 'Rail_RIO_01'}]
            }
        ]
        
        # 创建测试电缆（需要Marshalling机柜）
        cable = {
            'name': 'Cable_Test_DI',
            'io_points': [
                type('IOPoint', (), {
                    'tag': '1105-FZALL-10202-1',
                    'wiring_typical': 'DI NIS REMOTE'  # 需要Marshalling机柜
                })()
            ]
        }
        
        print(f"测试场景:")
        print(f"  电缆: {cable['name']}")
        print(f"  I/O点: {cable['io_points'][0].tag}")
        print(f"  典型回路: {cable['io_points'][0].wiring_typical}")
        print(f"  需要机柜类型: Marshalling")
        
        print(f"\n可用机柜:")
        for cabinet in cabinets:
            name = cabinet['name']
            cabinet_type = cabinet['type']
            print(f"  - {name}: {cabinet_type}")
        
        # 测试智能机柜选择
        print(f"\n开始智能机柜选择...")
        selected_cabinet = allocator._select_target_cabinet(cable, cabinets)
        
        if selected_cabinet:
            selected_name = selected_cabinet.get('name', 'Unknown')
            selected_type = selected_cabinet.get('type', 'Unknown')
            print(f"✅ 选择的机柜: {selected_name} ({selected_type})")
            
            # 验证选择是否正确
            if selected_type in ['Marshalling', 'System/Marshalling']:
                print(f"✅ 机柜类型匹配正确")
                return True
            else:
                print(f"❌ 机柜类型不匹配，期望Marshalling或System/Marshalling，实际{selected_type}")
                return False
        else:
            print(f"❌ 未选择任何机柜")
            return False
        
    except Exception as e:
        print(f"❌ 机柜选择测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pidb_cabinet_no_access():
    """测试PIDB机柜编号访问"""
    print("\n=== PIDB机柜编号访问测试 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        data_loader = DataLoader({})
        
        # 模拟PIDB数据行
        import pandas as pd
        
        test_row = pd.Series({
            'Cabinet': '1105-SIS-BAR-101',
            'CabinetTemplate': 'PPG BAR',
            'CabinetNo': '02'
        })
        
        column_mapping = {
            'cabinet_name': 'Cabinet',
            'template': 'CabinetTemplate',
            'cabinet_no': 'CabinetNo'
        }
        
        # 测试机柜数据提取
        cabinet_data = data_loader._extract_cabinet_from_row(test_row, column_mapping, 0)
        
        if cabinet_data:
            name = cabinet_data.get('name', 'Unknown')
            template = cabinet_data.get('template', 'Unknown')
            cabinet_no = cabinet_data.get('cabinet_no', '')
            
            print(f"✅ 机柜数据提取成功:")
            print(f"  名称: {name}")
            print(f"  模板: {template}")
            print(f"  编号: {cabinet_no}")
            
            if cabinet_no:
                print(f"✅ 机柜编号字段可访问: {cabinet_no}")
                return True
            else:
                print(f"❌ 机柜编号字段为空")
                return False
        else:
            print(f"❌ 机柜数据提取失败")
            return False
        
    except Exception as e:
        print(f"❌ PIDB机柜编号访问测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始简化的机柜选择和编号访问测试...")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_cabinet_selection_logic())
    test_results.append(test_pidb_cabinet_no_access())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！")
        print("\n✅ 修复验证:")
        print("1. 智能机柜选择逻辑工作正常")
        print("2. PIDB机柜编号字段可正确访问")
        print("3. 机柜类型匹配算法正确")
        
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
