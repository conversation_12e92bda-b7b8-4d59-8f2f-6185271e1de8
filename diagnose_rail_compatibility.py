#!/usr/bin/env python3
"""
导轨兼容性诊断脚本
分析器件兼容性要求和机柜导轨配置
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def diagnose_component_compatibility():
    """诊断器件兼容性要求"""
    print("=== 器件兼容性诊断 ===")
    
    try:
        from core.wiring_typical_parser import WiringTypicalManager
        
        # 加载典型回路
        manager = WiringTypicalManager("01C_Wiring Typical")
        typicals = manager.load_all_typicals()
        
        # 查找包含问题器件的典型回路
        problem_components = ['FTB1', 'FTB2', '3000510-380C1R']
        
        print(f"查找包含问题器件的典型回路: {problem_components}")
        
        for typical_name, typical_def in typicals.items():
            # 检查 marshalling 器件
            marshalling_components = getattr(typical_def, 'marshalling_components', [])
            system_components = getattr(typical_def, 'system_components', [])
            
            found_components = []
            
            # 检查 marshalling 器件
            for comp in marshalling_components:
                comp_name = getattr(comp, 'name', '')
                if any(problem_comp in comp_name for problem_comp in problem_components):
                    found_components.append({
                        'name': comp_name,
                        'type': getattr(comp, 'type', 'Unknown'),
                        'cabinet_location': 'Marshalling',
                        'part_types': getattr(comp, 'part_types', [])
                    })
            
            # 检查 system 器件
            for comp in system_components:
                comp_name = getattr(comp, 'name', '')
                if any(problem_comp in comp_name for problem_comp in problem_components):
                    found_components.append({
                        'name': comp_name,
                        'type': getattr(comp, 'type', 'Unknown'),
                        'cabinet_location': 'System',
                        'part_types': getattr(comp, 'part_types', [])
                    })
            
            if found_components:
                print(f"\n典型回路: {typical_name}")
                print(f"  机柜类型要求: {getattr(typical_def, 'required_cabinet_type', 'Unknown')}")
                for comp in found_components:
                    print(f"  器件: {comp['name']}")
                    print(f"    类型: {comp['type']}")
                    print(f"    位置: {comp['cabinet_location']}")
                    print(f"    兼容PartType: {comp['part_types']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 器件兼容性诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_cabinet_rail_configuration():
    """诊断机柜导轨配置"""
    print("\n=== 机柜导轨配置诊断 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        # 加载PIDB数据
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        data_loader = DataLoader({})
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        # 创建机柜配置
        cabinet_profiles = {
            'PPG REL': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_REL_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            }
        }
        
        # 创建数据模型
        models = data_loader.create_data_models(
            {'io_points': [], 'cables': {}},
            pidb_data,
            cabinet_profiles,
            {}
        )
        
        cabinets = models.get('cabinets', [])
        
        # 查找问题机柜
        target_cabinet = None
        for cabinet in cabinets:
            if cabinet.get('name') == '1105-SIS-REL-101':
                target_cabinet = cabinet
                break
        
        if target_cabinet:
            print(f"机柜: {target_cabinet.get('name', 'Unknown')}")
            print(f"类型: {target_cabinet.get('type', 'Unknown')}")
            print(f"模板: {target_cabinet.get('template', 'Unknown')}")
            
            rails = target_cabinet.get('rails', [])
            print(f"导轨数量: {len(rails)}")
            
            for i, rail in enumerate(rails, 1):
                print(f"\n  导轨 {i}: {rail.get('name', 'Unknown')}")
                print(f"    位置: {rail.get('position', 'Unknown')}")
                print(f"    长度: {rail.get('length', 0)} mm")
                print(f"    I/O类型: {rail.get('io_type', 'Unknown')}")
                print(f"    本安类型: {rail.get('intrinsic', 'Unknown')}")
                print(f"    电压等级: {rail.get('voltage_level', 0)} V")
                
                # 检查 PartType 配置
                part_types = []
                for j in range(1, 11):  # 检查 PartType01 到 PartType10
                    part_type_key = f'PartType{j:02d}'
                    part_type_value = rail.get(part_type_key)
                    if part_type_value:
                        part_types.append(part_type_value)
                
                print(f"    支持的PartType: {part_types}")
                print(f"    预留空间: {rail.get('reserved_from', 0)} - {rail.get('reserved_to', 0)} mm")
        else:
            print("❌ 未找到机柜 1105-SIS-REL-101")
            
            # 显示所有可用机柜
            print(f"\n可用机柜列表:")
            for cabinet in cabinets:
                name = cabinet.get('name', 'Unknown')
                cabinet_type = cabinet.get('type', 'Unknown')
                template = cabinet.get('template', 'Unknown')
                print(f"  - {name} (类型: {cabinet_type}, 模板: {template})")
        
        return True
        
    except Exception as e:
        print(f"❌ 机柜导轨配置诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def diagnose_rail_matching_algorithm():
    """诊断导轨匹配算法"""
    print("\n=== 导轨匹配算法诊断 ===")
    
    try:
        from core.component_allocation_engine import ComponentAllocationEngine
        
        # 创建分配引擎
        engine = ComponentAllocationEngine("01C_Wiring Typical")
        
        # 模拟器件兼容性检查
        test_components = [
            {'name': 'FTB1', 'type': 'FieldTermIn'},
            {'name': 'FTB2', 'type': 'FieldTermIn'},
            {'name': '3000510-380C1R', 'type': 'ETP'}
        ]
        
        # 模拟导轨配置
        test_rail = {
            'name': 'Rail_REL_01',
            'position': 'Marshalling',
            'length': 2000.0,
            'io_type': 'Mixed',
            'intrinsic': 'NIS',
            'voltage_level': 24,
            'PartType01': 'FieldTermIn',
            'PartType02': 'FieldTermOut',
            'PartType03': 'ETP',
            'PartType04': 'Barrier',
            'reserved_from': 0.0,
            'reserved_to': 0.0
        }
        
        print(f"测试导轨配置:")
        print(f"  名称: {test_rail['name']}")
        print(f"  位置: {test_rail['position']}")
        print(f"  支持的PartType: FieldTermIn, FieldTermOut, ETP, Barrier")
        
        print(f"\n测试器件兼容性:")
        for comp in test_components:
            comp_name = comp['name']
            comp_type = comp['type']
            
            # 检查兼容性
            is_compatible = comp_type in ['FieldTermIn', 'FieldTermOut', 'ETP', 'Barrier']
            
            print(f"  器件: {comp_name} (类型: {comp_type})")
            print(f"    理论兼容性: {'✅ 兼容' if is_compatible else '❌ 不兼容'}")
            
            # 如果有实际的匹配方法，也可以测试
            if hasattr(engine, '_is_component_compatible_with_rail'):
                try:
                    actual_compatible = engine._is_component_compatible_with_rail(comp, test_rail)
                    print(f"    实际匹配结果: {'✅ 兼容' if actual_compatible else '❌ 不兼容'}")
                except Exception as e:
                    print(f"    实际匹配测试失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 导轨匹配算法诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主诊断函数"""
    print("开始导轨兼容性问题诊断...")
    print("问题描述: 器件 FTB1、FTB2、3000510-380C1R 在机柜 1105-SIS-REL-101 中无兼容导轨")
    
    test_results = []
    
    # 执行诊断
    test_results.append(diagnose_component_compatibility())
    test_results.append(diagnose_cabinet_rail_configuration())
    test_results.append(diagnose_rail_matching_algorithm())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 诊断结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("✅ 诊断完成，已收集到问题分析信息")
        print("\n🔍 问题分析建议:")
        print("1. 检查器件的 PartType 要求是否与导轨的 PartType 配置匹配")
        print("2. 验证导轨匹配算法是否正确实现")
        print("3. 确认机柜轮询机制在导轨不兼容时是否工作")
        print("4. 检查导轨空间是否足够容纳所有器件")
    else:
        print("⚠️  部分诊断失败，需要进一步检查系统配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
