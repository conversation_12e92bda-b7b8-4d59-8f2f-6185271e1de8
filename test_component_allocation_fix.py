#!/usr/bin/env python3
"""
测试组件分配引擎修复效果
验证器件类型与机柜类型匹配、器件过滤、日志显示等问题的修复
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger import get_logger
from core.component_allocation_engine import ComponentAllocationEngine
from core.component_allocation_models import ActualCabinet, RailInstance, ComponentDefinition, ComponentType
from core.data_models import IOPoint, SignalType

def create_test_cabinet(cabinet_type: str = "Marshalling") -> ActualCabinet:
    """创建测试机柜"""
    # 创建测试导轨
    rail = RailInstance(
        name="TestRail",
        position="Front",
        length=2000.0,
        io_type="Mixed",
        intrinsic="NIS",
        voltage_level=24,
        supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "Barrier", "IOModule", "Chassis"]
    )
    
    cabinet = ActualCabinet(
        name="E-1001",
        template_name="PPG BAR",
        location="Test Area",
        cabinet_type=cabinet_type,
        rails=[rail]
    )
    
    return cabinet

def create_test_io_point() -> IOPoint:
    """创建测试I/O点"""
    return IOPoint(
        tag="1105-ACK-10001",
        signal_type=SignalType.DI,
        description="Test I/O Point",
        wiring_typical="AI_4-20mA_2W_NIS_Barrier"
    )

def test_component_filtering():
    """测试器件过滤功能"""
    print("\n=== 测试器件过滤功能 ===")
    
    try:
        from core.wiring_typical_parser import WiringTypicalParser
        import xml.etree.ElementTree as ET
        
        parser = WiringTypicalParser()
        
        # 创建测试XML元素
        # 1. 正常器件（应该被解析）
        normal_component = ET.Element("ProfileComponent", Name="FTB1", Type="HardwarePart", Count="1")
        normal_props = ET.SubElement(normal_component, "ProfileProperties")
        ET.SubElement(normal_props, "ProfileProperty", Name="HardwareType", Value="FieldTermIn")
        
        # 2. Wires组件（应该被过滤）
        wire_component = ET.Element("ProfileComponent", Name="Wires", Type="Wire", Count="1")
        
        # 3. 没有HardwareType的组件（应该被过滤）
        no_type_component = ET.Element("ProfileComponent", Name="SomeConnection", Type="Connection", Count="1")
        
        # 测试解析
        normal_result = parser._parse_component_element(normal_component)
        wire_result = parser._parse_component_element(wire_component)
        no_type_result = parser._parse_component_element(no_type_component)
        
        print(f"✅ 正常器件解析结果: {normal_result.name if normal_result else 'None'}")
        print(f"✅ Wires组件过滤结果: {'已过滤' if wire_result is None else '未过滤'}")
        print(f"✅ 无类型组件过滤结果: {'已过滤' if no_type_result is None else '未过滤'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 器件过滤测试失败: {e}")
        return False

def test_component_type_mapping():
    """测试器件类型映射"""
    print("\n=== 测试器件类型映射 ===")
    
    try:
        from core.wiring_typical_parser import WiringTypicalParser
        
        parser = WiringTypicalParser()
        
        # 测试不同器件类型的映射
        test_cases = [
            ("FieldTermIn", "FTB1", ComponentType.FIELD_TERM_IN),
            ("FieldTermOut", "FTB2", ComponentType.FIELD_TERM_OUT),
            ("Barrier", "3000510-380C1R", ComponentType.BARRIER),
            ("Chassis", "TCN_RXM_Chassis", ComponentType.CHASSIS),
            ("RxmChassis", "TCN_RXM_Chassis", ComponentType.CHASSIS),
            ("", "FTB3", ComponentType.FIELD_TERM_IN),  # 无HardwareType时根据名称推断
        ]
        
        for hardware_type, component_name, expected_type in test_cases:
            result_type = parser._map_hardware_type_to_component_type(hardware_type, component_name)
            status = "✅" if result_type == expected_type else "❌"
            print(f"{status} {hardware_type} + {component_name} -> {result_type.value} (期望: {expected_type.value})")
        
        return True
        
    except Exception as e:
        print(f"❌ 器件类型映射测试失败: {e}")
        return False

def test_cabinet_compatibility():
    """测试机柜兼容性检查"""
    print("\n=== 测试机柜兼容性检查 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        
        # 创建不同类型的器件
        ftb_component = ComponentDefinition(
            name="FTB1",
            component_type=ComponentType.FIELD_TERM_IN,
            part_number="FTB-1",
            width=12.5
        )
        
        chassis_component = ComponentDefinition(
            name="TCN_RXM_Chassis",
            component_type=ComponentType.CHASSIS,
            part_number="TCN-RXM",
            width=100.0
        )
        
        # 创建不同类型的机柜
        marshalling_cabinet = create_test_cabinet("Marshalling")
        system_cabinet = create_test_cabinet("System")
        mixed_cabinet = create_test_cabinet("System/Marshalling")
        
        # 测试兼容性
        test_cases = [
            (ftb_component, marshalling_cabinet, True, "FTB1 -> Marshalling Cabinet"),
            (ftb_component, system_cabinet, False, "FTB1 -> System Cabinet"),
            (ftb_component, mixed_cabinet, True, "FTB1 -> Mixed Cabinet"),
            (chassis_component, marshalling_cabinet, False, "Chassis -> Marshalling Cabinet"),
            (chassis_component, system_cabinet, True, "Chassis -> System Cabinet"),
            (chassis_component, mixed_cabinet, True, "Chassis -> Mixed Cabinet"),
        ]
        
        for component, cabinet, expected, description in test_cases:
            result = engine._validate_component_cabinet_compatibility(component, cabinet)
            status = "✅" if result == expected else "❌"
            print(f"{status} {description}: {result} (期望: {expected})")
        
        return True
        
    except Exception as e:
        print(f"❌ 机柜兼容性测试失败: {e}")
        return False

def test_allocation_with_logging():
    """测试分配过程的日志记录"""
    print("\n=== 测试分配过程日志记录 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        cabinet = create_test_cabinet("Marshalling")
        io_point = create_test_io_point()
        
        # 模拟分配过程（这里只是测试日志格式，不执行实际分配）
        print(f"✅ 测试机柜名称显示: {cabinet.name}")
        print(f"✅ 测试机柜类型显示: {cabinet.cabinet_type}")
        print(f"✅ 测试I/O点标签显示: {io_point.tag}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日志记录测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试组件分配引擎修复效果...")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_component_filtering())
    test_results.append(test_component_type_mapping())
    test_results.append(test_cabinet_compatibility())
    test_results.append(test_allocation_with_logging())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！组件分配引擎修复成功。")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
