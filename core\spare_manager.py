"""
Spare点管理器
负责管理spare点的生成和分配策略
"""

import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from collections import Counter
from enum import Enum

from core.logger import get_logger
from core.data_models import IOPoint, SignalType


class SpareStrategy(Enum):
    """Spare点生成策略"""
    CABLE_FORM = "CABLE_FORM"  # 电缆策略
    ETP_FORM = "ETP_FORM"      # ETP策略


@dataclass
class SpareConfiguration:
    """Spare点配置"""
    default_spare_limit: int = 2
    etp_spare_limits: Dict[str, int] = None
    enable_cable_spare: bool = True
    enable_etp_spare: bool = True
    spare_naming_prefix: str = "SPARE_"
    spare_description: str = "-"
    
    def __post_init__(self):
        if self.etp_spare_limits is None:
            self.etp_spare_limits = {
                "CPM16-AI3700": 2,
                "CPM16-AO3700": 2,
                "CPM16-DI3700": 2,
                "CPM16-DO3700": 2
            }
    
    def get_spare_limit(self, etp_type: str) -> int:
        """获取指定ETP类型的spare下限"""
        return self.etp_spare_limits.get(etp_type, self.default_spare_limit)


@dataclass
class SpareGenerationContext:
    """Spare点生成上下文"""
    cables: List[Dict[str, Any]] = None
    terminal_blocks: List[Any] = None
    wiring_typicals: Dict[str, Any] = None
    cabinets: List[Dict[str, Any]] = None
    config: SpareConfiguration = None
    
    def __post_init__(self):
        if self.cables is None:
            self.cables = []
        if self.terminal_blocks is None:
            self.terminal_blocks = []
        if self.wiring_typicals is None:
            self.wiring_typicals = {}
        if self.cabinets is None:
            self.cabinets = []


@dataclass
class SpareGenerationResult:
    """Spare点生成结果"""
    success: bool = False
    spare_points: List[IOPoint] = None
    errors: List[str] = None
    warnings: List[str] = None
    summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.spare_points is None:
            self.spare_points = []
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.summary is None:
            self.summary = {}


class SpareStrategyBase(ABC):
    """Spare点生成策略基类"""
    
    def __init__(self, config: SpareConfiguration):
        self.config = config
        self.logger = get_logger(__name__)
    
    @abstractmethod
    def generate_spare_points(self, context: SpareGenerationContext) -> SpareGenerationResult:
        """生成spare点"""
        pass
    
    @abstractmethod
    def count_typical_circuits(self, io_points: List[IOPoint]) -> Dict[str, int]:
        """统计典型回路类型"""
        pass
    
    def _get_most_common_typical_circuit(self, io_points: List[IOPoint]) -> str:
        """获取最常用的典型回路类型"""
        circuit_counts = self.count_typical_circuits(io_points)
        if not circuit_counts:
            return "DI NIS N"  # 默认典型回路
        
        # 返回计数最多的典型回路
        return max(circuit_counts.items(), key=lambda x: x[1])[0]
    
    def _create_spare_point(self, cable_name: str, pair_number: int,
                           typical_circuit: str, signal_type: SignalType,
                           spare_index: int) -> IOPoint:
        """创建spare点"""
        spare_point = IOPoint(
            tag=f"{self.config.spare_naming_prefix}{spare_index}",
            signal_type=signal_type,
            description=self.config.spare_description,
            cable_name=cable_name,
            pair_number=pair_number,
            wiring_typical=typical_circuit,
            is_spare=True,
            allocation_status="Spare预留"  # 设置spare点的特殊状态
        )
        return spare_point


class CableSpareStrategy(SpareStrategyBase):
    """电缆Spare点策略"""
    
    def generate_spare_points(self, context: SpareGenerationContext) -> SpareGenerationResult:
        """为电缆中的空线对生成spare点"""
        result = SpareGenerationResult()
        
        if not context.config.enable_cable_spare:
            result.success = True
            result.summary['message'] = "电缆spare点生成已禁用"
            return result
        
        try:
            spare_counter = 1
            total_spare_points = 0
            
            for cable_data in context.cables:
                cable_name = cable_data.get('name', '')
                pair_size = cable_data.get('pair_size', 0)
                io_points = cable_data.get('io_points', [])
                
                if pair_size <= 0 or not io_points:
                    continue
                
                # 获取已使用的线对
                used_pairs = set()
                for point in io_points:
                    if hasattr(point, 'pair_number') and point.pair_number > 0:
                        used_pairs.add(point.pair_number)
                
                # 计算空线对
                available_pairs = []
                for pair_num in range(1, pair_size + 1):
                    if pair_num not in used_pairs:
                        available_pairs.append(pair_num)
                
                if not available_pairs:
                    continue
                
                # 获取最常用的典型回路类型
                most_common_circuit = self._get_most_common_typical_circuit(io_points)
                
                # 确定信号类型（基于最常用的典型回路）
                signal_type = self._infer_signal_type_from_circuit(most_common_circuit)
                
                # 为每个空线对创建spare点
                for pair_num in available_pairs:
                    spare_point = self._create_spare_point(
                        cable_name, pair_num, most_common_circuit, 
                        signal_type, spare_counter
                    )
                    result.spare_points.append(spare_point)
                    spare_counter += 1
                    total_spare_points += 1
                
                self.logger.info(f"为电缆 {cable_name} 生成了 {len(available_pairs)} 个spare点")
            
            result.success = True
            result.summary = {
                'strategy': 'CABLE_FORM',
                'total_spare_points': total_spare_points,
                'processed_cables': len(context.cables)
            }
            
            self.logger.info(f"电缆策略生成了 {total_spare_points} 个spare点")
            
        except Exception as e:
            self.logger.error(f"电缆spare点生成失败: {e}")
            result.errors.append(f"电缆spare点生成异常: {e}")
            result.success = False
        
        return result
    
    def count_typical_circuits(self, io_points: List[IOPoint]) -> Dict[str, int]:
        """统计典型回路类型"""
        circuit_counts = Counter()
        for point in io_points:
            if hasattr(point, 'wiring_typical') and point.wiring_typical:
                circuit_counts[point.wiring_typical] += 1
        return dict(circuit_counts)
    
    def _infer_signal_type_from_circuit(self, circuit_name: str) -> SignalType:
        """从典型回路名称推断信号类型"""
        circuit_upper = circuit_name.upper()
        if 'AI' in circuit_upper:
            return SignalType.AI
        elif 'AO' in circuit_upper:
            return SignalType.AO
        elif 'DO' in circuit_upper:
            return SignalType.DO
        else:
            return SignalType.DI  # 默认为DI


class EtpSpareStrategy(SpareStrategyBase):
    """ETP Spare点策略"""
    
    def generate_spare_points(self, context: SpareGenerationContext) -> SpareGenerationResult:
        """根据ETP容量和配置生成spare点"""
        result = SpareGenerationResult()
        
        if not context.config.enable_etp_spare:
            result.success = True
            result.summary['message'] = "ETP spare点生成已禁用"
            return result
        
        try:
            spare_counter = 1
            total_spare_points = 0
            
            # 按ETP类型分组terminal blocks
            etp_groups = self._group_terminal_blocks_by_etp(context.terminal_blocks)
            
            for etp_type, blocks in etp_groups.items():
                spare_limit = context.config.get_spare_limit(etp_type)
                
                for block in blocks:
                    current_spare_count = self._count_existing_spares(block)
                    needed_spares = max(0, spare_limit - current_spare_count)
                    
                    if needed_spares > 0 and block.available_points >= needed_spares:
                        # 获取该ETP中最常用的典型回路
                        most_common_circuit = self._get_most_common_typical_circuit(block.io_points)
                        signal_type = self._infer_signal_type_from_etp(etp_type)
                        
                        # 生成所需的spare点
                        for i in range(needed_spares):
                            spare_point = self._create_spare_point(
                                "", 0, most_common_circuit, signal_type, spare_counter
                            )
                            result.spare_points.append(spare_point)
                            spare_counter += 1
                            total_spare_points += 1
                        
                        self.logger.info(f"为ETP {etp_type} 的端子排 {block.name} 生成了 {needed_spares} 个spare点")
            
            result.success = True
            result.summary = {
                'strategy': 'ETP_FORM',
                'total_spare_points': total_spare_points,
                'processed_etps': len(etp_groups)
            }
            
            self.logger.info(f"ETP策略生成了 {total_spare_points} 个spare点")
            
        except Exception as e:
            self.logger.error(f"ETP spare点生成失败: {e}")
            result.errors.append(f"ETP spare点生成异常: {e}")
            result.success = False
        
        return result
    
    def count_typical_circuits(self, io_points: List[IOPoint]) -> Dict[str, int]:
        """统计典型回路类型"""
        circuit_counts = Counter()
        for point in io_points:
            if hasattr(point, 'wiring_typical') and point.wiring_typical:
                circuit_counts[point.wiring_typical] += 1
        return dict(circuit_counts)
    
    def _group_terminal_blocks_by_etp(self, terminal_blocks: List[Any]) -> Dict[str, List[Any]]:
        """按ETP类型分组端子排"""
        groups = {}
        for block in terminal_blocks:
            etp_type = getattr(block, 'etp_type', 'Unknown')
            if etp_type not in groups:
                groups[etp_type] = []
            groups[etp_type].append(block)
        return groups
    
    def _count_existing_spares(self, terminal_block: Any) -> int:
        """统计端子排中现有的spare点数量"""
        spare_count = 0
        for point in getattr(terminal_block, 'io_points', []):
            if hasattr(point, 'is_spare') and point.is_spare:
                spare_count += 1
        return spare_count
    
    def _infer_signal_type_from_etp(self, etp_type: str) -> SignalType:
        """从ETP类型推断信号类型"""
        etp_upper = etp_type.upper()
        if 'AI' in etp_upper:
            return SignalType.AI
        elif 'AO' in etp_upper:
            return SignalType.AO
        elif 'DO' in etp_upper:
            return SignalType.DO
        else:
            return SignalType.DI  # 默认为DI


class SparePointManager:
    """Spare点管理器主类"""

    def __init__(self, config: Dict[str, Any]):
        """
        初始化Spare点管理器

        Args:
            config: 系统配置字典
        """
        self.logger = get_logger(__name__)

        # 加载spare配置
        spare_settings = config.get('spare_settings', {})
        self.spare_config = SpareConfiguration(
            default_spare_limit=spare_settings.get('default_spare_limit', 2),
            etp_spare_limits=spare_settings.get('etp_spare_limits', {}),
            enable_cable_spare=spare_settings.get('enable_cable_spare', True),
            enable_etp_spare=spare_settings.get('enable_etp_spare', True),
            spare_naming_prefix=spare_settings.get('spare_naming_prefix', 'SPARE_'),
            spare_description=spare_settings.get('spare_description', '-')
        )

        # 初始化策略
        self.cable_strategy = CableSpareStrategy(self.spare_config)
        self.etp_strategy = EtpSpareStrategy(self.spare_config)

        self.logger.info("Spare点管理器初始化完成")

    def generate_spare_points(self, cables: List[Dict[str, Any]],
                             terminal_blocks: List[Any],
                             wiring_typicals: Dict[str, Any],
                             cabinets: List[Dict[str, Any]],
                             strategy: SpareStrategy = SpareStrategy.CABLE_FORM) -> SpareGenerationResult:
        """
        生成spare点

        Args:
            cables: 电缆列表
            terminal_blocks: 端子排列表
            wiring_typicals: 典型回路字典
            cabinets: 机柜列表
            strategy: 生成策略

        Returns:
            Spare点生成结果
        """
        self.logger.info(f"开始生成spare点，策略: {strategy.value}")

        # 创建生成上下文
        context = SpareGenerationContext(
            cables=cables,
            terminal_blocks=terminal_blocks,
            wiring_typicals=wiring_typicals,
            cabinets=cabinets,
            config=self.spare_config
        )

        try:
            if strategy == SpareStrategy.CABLE_FORM:
                result = self.cable_strategy.generate_spare_points(context)
            elif strategy == SpareStrategy.ETP_FORM:
                result = self.etp_strategy.generate_spare_points(context)
            else:
                # 默认使用电缆策略
                result = self.cable_strategy.generate_spare_points(context)

            if result.success:
                self.logger.info(f"Spare点生成成功，共生成 {len(result.spare_points)} 个spare点")
            else:
                self.logger.warning(f"Spare点生成失败，错误数: {len(result.errors)}")

            return result

        except Exception as e:
            self.logger.error(f"Spare点生成异常: {e}")
            result = SpareGenerationResult()
            result.errors.append(f"Spare点生成异常: {e}")
            result.success = False
            return result

    def generate_both_strategies(self, cables: List[Dict[str, Any]],
                                terminal_blocks: List[Any],
                                wiring_typicals: Dict[str, Any],
                                cabinets: List[Dict[str, Any]]) -> SpareGenerationResult:
        """
        同时执行电缆和ETP两种策略

        Args:
            cables: 电缆列表
            terminal_blocks: 端子排列表
            wiring_typicals: 典型回路字典
            cabinets: 机柜列表

        Returns:
            合并的Spare点生成结果
        """
        self.logger.info("执行电缆和ETP双策略spare点生成")

        # 先执行电缆策略
        cable_result = self.generate_spare_points(
            cables, terminal_blocks, wiring_typicals, cabinets,
            SpareStrategy.CABLE_FORM
        )

        # 再执行ETP策略
        etp_result = self.generate_spare_points(
            cables, terminal_blocks, wiring_typicals, cabinets,
            SpareStrategy.ETP_FORM
        )

        # 合并结果
        combined_result = SpareGenerationResult()
        combined_result.spare_points.extend(cable_result.spare_points)
        combined_result.spare_points.extend(etp_result.spare_points)
        combined_result.errors.extend(cable_result.errors)
        combined_result.errors.extend(etp_result.errors)
        combined_result.warnings.extend(cable_result.warnings)
        combined_result.warnings.extend(etp_result.warnings)

        combined_result.success = cable_result.success and etp_result.success
        combined_result.summary = {
            'total_spare_points': len(combined_result.spare_points),
            'cable_strategy_points': len(cable_result.spare_points),
            'etp_strategy_points': len(etp_result.spare_points),
            'strategies_used': ['CABLE_FORM', 'ETP_FORM']
        }

        self.logger.info(f"双策略spare点生成完成，总计 {len(combined_result.spare_points)} 个spare点")

        return combined_result

    def update_configuration(self, new_config: Dict[str, Any]):
        """
        更新spare配置

        Args:
            new_config: 新的配置字典
        """
        spare_settings = new_config.get('spare_settings', {})

        # 更新配置
        self.spare_config.default_spare_limit = spare_settings.get(
            'default_spare_limit', self.spare_config.default_spare_limit
        )
        self.spare_config.etp_spare_limits.update(
            spare_settings.get('etp_spare_limits', {})
        )
        self.spare_config.enable_cable_spare = spare_settings.get(
            'enable_cable_spare', self.spare_config.enable_cable_spare
        )
        self.spare_config.enable_etp_spare = spare_settings.get(
            'enable_etp_spare', self.spare_config.enable_etp_spare
        )
        self.spare_config.spare_naming_prefix = spare_settings.get(
            'spare_naming_prefix', self.spare_config.spare_naming_prefix
        )
        self.spare_config.spare_description = spare_settings.get(
            'spare_description', self.spare_config.spare_description
        )

        # 重新初始化策略
        self.cable_strategy = CableSpareStrategy(self.spare_config)
        self.etp_strategy = EtpSpareStrategy(self.spare_config)

        self.logger.info("Spare配置已更新")
