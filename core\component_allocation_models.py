"""
器件分配相关的数据模型
支持基于典型回路的多器件分配
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional, Any
from enum import Enum


class ComponentType(Enum):
    """器件类型枚举"""
    FIELD_TERM_IN = "FieldTermIn"      # 现场端子输入
    FIELD_TERM_OUT = "FieldTermOut"    # 现场端子输出
    ETP = "ETP"                        # 电子端子排
    BARRIER = "Barrier"                # 安全栅
    CONNECTOR = "Connector"            # 连接器
    IO_MODULE = "IOModule"             # I/O模块
    CHASSIS = "Chassis"                # 机架
    WIRE = "Wire"                      # 导线


class MountingType(Enum):
    """安装方式枚举"""
    RAIL = "Rail"                      # 导轨安装
    RACK = "Rack"                      # 机架安装
    PANEL = "Panel"                    # 面板安装


@dataclass
class ComponentDefinition:
    """典型回路中定义的器件"""
    name: str                          # 器件名称（如FTB1, CPM16-AI3700）
    component_type: ComponentType      # 器件类型
    part_number: str                   # 器件型号
    count: int = 1                     # 数量
    width: Optional[float] = None      # 宽度(mm)
    length: Optional[float] = None     # 长度(mm)
    height: Optional[float] = None     # 高度(mm)
    mounting_type: MountingType = MountingType.RAIL
    channel_count: Optional[int] = None  # 通道数量
    slots: List[str] = field(default_factory=list)  # 槽位列表
    cabinet_location: str = "MarshallingCabinet"  # 器件在典型回路中的机柜位置
    properties: Dict[str, Any] = field(default_factory=dict)  # 其他属性
    
    @property
    def space_requirement(self) -> float:
        """获取空间需求（mm）"""
        return self.width or self.length or 0.0
    
    @property
    def effective_channel_count(self) -> int:
        """获取有效通道数量"""
        if self.channel_count is not None:
            return self.channel_count
        elif self.slots:
            return len(self.slots)
        else:
            return 1


@dataclass
class ComponentAllocation:
    """单个器件的分配结果"""
    component_definition: ComponentDefinition  # 器件定义
    cabinet_name: str                          # 实际机柜名称（如E-1001）
    rail_name: str                             # 导轨名称（如Rail_A）
    position_start: float                      # 在导轨上的起始位置(mm)
    position_end: float                        # 在导轨上的结束位置(mm)

    # 新增字段 - 基于用户需求
    component_name: str = ""                   # 生成的器件名称
    hardware_type: str = ""                    # 器件类型(HardwareType)
    part_number: str = ""                      # 器件型号(PartNumber)
    sequence_on_rail: int = 0                  # 器件在导轨上的序号
    screw_id: str = ""                         # 器件的ScrewID

    # 原有字段
    channel: Optional[int] = None              # 分配的通道号
    slot: Optional[str] = None                 # 分配的槽位
    terminal_numbers: List[str] = field(default_factory=list)  # 端子号列表
    connector_info: Dict[str, str] = field(default_factory=dict)  # 连接器信息

    # Wire连接信息
    wire_connections: List[Dict[str, Any]] = field(default_factory=list)  # Wire连接信息
    
    @property
    def allocation_identifier(self) -> str:
        """获取分配标识符"""
        parts = [self.cabinet_name, self.rail_name]
        if self.slot:
            parts.append(f"S{self.slot}")
        if self.channel:
            parts.append(f"CH{self.channel}")
        return "_".join(parts)

    def to_csv_dict(self) -> Dict[str, Any]:
        """转换为CSV导出格式的字典"""
        return {
            'component_name': self.component_name,
            'hardware_type': self.hardware_type,
            'part_number': self.part_number,
            'cabinet_name': self.cabinet_name,
            'rail_name': self.rail_name,
            'sequence_on_rail': self.sequence_on_rail,
            'screw_id': self.screw_id,
            'position_start_mm': self.position_start,
            'position_end_mm': self.position_end,
            'channel': self.channel,
            'slot': self.slot,
            'terminal_numbers': ','.join(self.terminal_numbers) if self.terminal_numbers else '',
            'allocation_identifier': self.allocation_identifier
        }


@dataclass
class RailInstance:
    """导轨实例"""
    name: str                                  # 导轨名称（如Rail_A）
    position: str                              # 导轨位置描述
    length: float                              # 导轨长度(mm)
    io_type: str                               # I/O类型（Analog/Digital/Mixed）
    intrinsic: str                             # 本安类型（IS/NIS/Mixed）
    voltage_level: int                         # 电压等级
    supported_part_types: List[str] = field(default_factory=list)  # 支持的器件类型
    reserved_from: float = 0.0                 # 保留起始位置(mm)
    reserved_to: float = 0.0                   # 保留结束位置(mm)
    allocated_components: List[ComponentAllocation] = field(default_factory=list)  # 已分配的器件
    
    @property
    def available_length(self) -> float:
        """获取可用长度"""
        reserved_length = self.reserved_to - self.reserved_from
        allocated_length = sum(
            comp.position_end - comp.position_start 
            for comp in self.allocated_components
        )
        return self.length - reserved_length - allocated_length
    
    @property
    def next_available_position(self) -> float:
        """获取下一个可用位置"""
        if not self.allocated_components:
            return self.reserved_from
        
        # 找到最后一个分配器件的结束位置
        last_position = max(
            comp.position_end for comp in self.allocated_components
        )
        return max(last_position, self.reserved_from)
    
    def can_accommodate(self, component: ComponentDefinition) -> bool:
        """检查是否能容纳指定器件"""
        # 检查器件类型兼容性
        if component.component_type.value not in self.supported_part_types:
            return False
        
        # 检查空间是否足够
        required_space = component.space_requirement
        return self.available_length >= required_space
    
    def allocate_component(self, component: ComponentDefinition, cabinet_name: str) -> ComponentAllocation:
        """在导轨上分配器件"""
        if not self.can_accommodate(component):
            raise ValueError(f"导轨 {self.name} 无法容纳器件 {component.name}")
        
        start_pos = self.next_available_position
        end_pos = start_pos + component.space_requirement
        
        allocation = ComponentAllocation(
            component_definition=component,
            cabinet_name=cabinet_name,
            rail_name=self.name,
            position_start=start_pos,
            position_end=end_pos
        )
        
        self.allocated_components.append(allocation)
        return allocation


@dataclass
class ActualCabinet:
    """实际机柜实例"""
    name: str                                  # 实际机柜名称（如E-1001）
    template_name: str                         # 典型机柜模板名称（如PPG BAR）
    location: str                              # 机柜位置
    cabinet_type: str                          # 机柜类型（Marshalling/System）
    rails: List[RailInstance] = field(default_factory=list)  # 导轨实例列表
    properties: Dict[str, Any] = field(default_factory=dict)  # 机柜属性
    
    def get_rail_by_name(self, rail_name: str) -> Optional[RailInstance]:
        """根据名称获取导轨"""
        for rail in self.rails:
            if rail.name == rail_name:
                return rail
        return None
    
    def find_compatible_rails(self, component: ComponentDefinition) -> List[RailInstance]:
        """查找与器件兼容的导轨"""
        compatible_rails = []
        for rail in self.rails:
            if rail.can_accommodate(component):
                compatible_rails.append(rail)
        return compatible_rails
    
    def get_total_allocated_components(self) -> List[ComponentAllocation]:
        """获取所有已分配的器件"""
        all_allocations = []
        for rail in self.rails:
            all_allocations.extend(rail.allocated_components)
        return all_allocations


@dataclass
class WiringTypicalDefinition:
    """典型回路定义"""
    name: str                                  # 典型回路名称
    signal_type: str                           # 信号类型
    intrinsic: str                             # 本安类型
    required_cabinet_type: str = 'Marshalling' # 要求的机柜类型
    marshalling_components: List[ComponentDefinition] = field(default_factory=list)  # 汇控柜器件
    system_components: List[ComponentDefinition] = field(default_factory=list)       # 系统柜器件
    cables: List[Dict[str, Any]] = field(default_factory=list)  # 电缆定义
    wires: List[Dict[str, Any]] = field(default_factory=list)   # 导线定义
    
    def get_marshalling_components(self) -> List[ComponentDefinition]:
        """获取需要在汇控柜中分配的器件"""
        return self.marshalling_components


@dataclass
class MultiComponentAllocationResult:
    """多器件分配结果"""
    io_point: Any                              # I/O点对象（避免循环导入）
    component_allocations: List[ComponentAllocation] = field(default_factory=list)
    success: bool = True
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    @property
    def primary_cabinet(self) -> Optional[str]:
        """获取主要机柜名称"""
        if self.component_allocations:
            return self.component_allocations[0].cabinet_name
        return None
    
    @property
    def allocated_rails(self) -> List[str]:
        """获取所有分配的导轨"""
        rails = set()
        for allocation in self.component_allocations:
            rails.add(allocation.rail_name)
        return list(rails)
    
    def get_allocations_by_type(self, component_type: ComponentType) -> List[ComponentAllocation]:
        """根据器件类型获取分配结果"""
        return [
            alloc for alloc in self.component_allocations
            if alloc.component_definition.component_type == component_type
        ]
