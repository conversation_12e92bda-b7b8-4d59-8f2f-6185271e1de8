"""
调试命名配置对话框
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def debug_dialog_creation():
    """调试对话框创建过程"""
    print("开始调试命名配置对话框创建...")
    
    try:
        from PySide6.QtWidgets import QApplication
        
        # 创建应用程序
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        print("1. ✓ QApplication创建成功")
        
        # 测试导入
        from gui.naming_config_dialog import NamingConfigDialog
        print("2. ✓ NamingConfigDialog导入成功")
        
        # 测试命名引擎导入
        from core.naming_engine import NamingEngine
        engine = NamingEngine()
        print("3. ✓ NamingEngine创建成功")
        
        # 创建简单的测试配置
        test_config = {
            'barrier': {
                'prefix': 'BA',
                'separator': '',
                'suffix': ''
            }
        }
        print("4. ✓ 测试配置创建成功")
        
        # 尝试创建对话框
        try:
            dialog = NamingConfigDialog(None, test_config)
            print("5. ✓ 对话框创建成功")
            
            # 测试对话框的基本属性
            print(f"   - 窗口标题: {dialog.windowTitle()}")
            print(f"   - 是否模态: {dialog.isModal()}")
            print(f"   - 器件类型数量: {len(dialog.device_types)}")
            
            # 测试方法是否存在
            methods_to_test = [
                '_setup_ui',
                '_load_current_config',
                '_update_rule_preview',
                '_generate_example',
                '_collect_config'
            ]
            
            for method in methods_to_test:
                if hasattr(dialog, method):
                    print(f"   - ✓ 方法 {method} 存在")
                else:
                    print(f"   - ✗ 方法 {method} 不存在")
            
            # 测试示例生成
            try:
                example = dialog._generate_example('barrier', 'BA', '', '')
                print(f"6. ✓ 示例生成成功: {example}")
            except Exception as e:
                print(f"6. ✗ 示例生成失败: {e}")
            
            # 测试配置收集
            try:
                config = dialog._collect_config()
                print(f"7. ✓ 配置收集成功: {len(config)} 个器件类型")
            except Exception as e:
                print(f"7. ✗ 配置收集失败: {e}")
            
            return True
            
        except Exception as e:
            print(f"5. ✗ 对话框创建失败: {e}")
            import traceback
            traceback.print_exc()
            return False
            
    except Exception as e:
        print(f"✗ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_allocation_widget():
    """调试分配界面的命名配置功能"""
    print("\n开始调试分配界面命名配置功能...")
    
    try:
        # 测试导入
        from gui.allocation_widget import AllocationWidget
        print("1. ✓ AllocationWidget导入成功")
        
        # 检查方法是否存在
        methods = [
            '_configure_naming_rules',
            '_load_naming_config',
            '_save_naming_config'
        ]
        
        for method in methods:
            if hasattr(AllocationWidget, method):
                print(f"2. ✓ 方法 {method} 存在")
            else:
                print(f"2. ✗ 方法 {method} 不存在")
        
        # 测试图标导入
        from gui.icons import get_icon
        try:
            settings_icon = get_icon('settings')
            print("3. ✓ settings图标获取成功")
        except Exception as e:
            print(f"3. ✗ settings图标获取失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 分配界面调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_simple_dialog():
    """创建一个简单的对话框测试"""
    print("\n开始简单对话框测试...")
    
    try:
        from PySide6.QtWidgets import QApplication, QDialog, QVBoxLayout, QLabel, QPushButton
        
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        # 创建简单对话框
        dialog = QDialog()
        dialog.setWindowTitle("测试对话框")
        dialog.resize(300, 200)
        
        layout = QVBoxLayout(dialog)
        label = QLabel("这是一个测试对话框")
        layout.addWidget(label)
        
        close_btn = QPushButton("关闭")
        close_btn.clicked.connect(dialog.accept)
        layout.addWidget(close_btn)
        
        print("✓ 简单对话框创建成功")
        
        # 不显示对话框，只测试创建
        return True
        
    except Exception as e:
        print(f"✗ 简单对话框测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主调试函数"""
    print("命名配置对话框调试工具\n")
    print("=" * 50)
    
    tests = [
        debug_simple_dialog,
        debug_allocation_widget,
        debug_dialog_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"调试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✓ 所有调试测试通过！")
    else:
        print("✗ 部分调试测试失败")
        print("\n建议:")
        print("1. 检查PySide6是否正确安装")
        print("2. 检查所有依赖模块是否正确导入")
        print("3. 检查对话框初始化过程中的异常")

if __name__ == '__main__':
    main()
