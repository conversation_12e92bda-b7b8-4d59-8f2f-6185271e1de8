"""
报表生成器模块
提供统一的报表生成接口和兼容性支持
"""

import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass

try:
    import openpyxl
    OPENPYXL_AVAILABLE = True
except ImportError:
    OPENPYXL_AVAILABLE = False

from core.logger import get_logger


@dataclass
class ReportTemplate:
    """
    统一的报表模板类
    兼容不同报表系统的需求
    """
    template_path: str
    name: str
    description: str = ""
    id: Optional[str] = None
    output_format: str = "xlsx"
    parameters: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.parameters is None:
            self.parameters = {}
        if self.id is None:
            self.id = self.name.lower().replace(' ', '_')
    
    def load_template(self):
        """
        加载Excel模板文件
        
        Returns:
            openpyxl.Workbook: 加载的工作簿对象
        """
        if not OPENPYXL_AVAILABLE:
            raise ImportError("需要安装 openpyxl 库来处理Excel文件")
        
        if not os.path.exists(self.template_path):
            raise FileNotFoundError(f"模板文件不存在: {self.template_path}")
        
        try:
            workbook = openpyxl.load_workbook(self.template_path)
            return workbook
        except Exception as e:
            raise Exception(f"加载模板文件失败: {e}")


# 从 io_report_builder 导入并重新导出类
from core.io_report_builder import (
    IOReportBuilder,
    ReportManager as _ReportManager,
    ReportGeneratorFactory as _ReportGeneratorFactory
)


class ReportManager(_ReportManager):
    """
    报表管理器 - 兼容性包装
    """
    pass


class ReportGeneratorFactory(_ReportGeneratorFactory):
    """
    报表生成器工厂 - 兼容性包装
    """
    pass


# 导出所有必要的类和函数
__all__ = [
    'ReportTemplate',
    'IOReportBuilder', 
    'ReportManager',
    'ReportGeneratorFactory'
]
