#!/usr/bin/env python3
"""
最终测试I/O点分配系统
验证所有修复是否正确工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_complete_allocation_system():
    """测试完整的分配系统"""
    print("=== 最终I/O点分配系统测试 ===")
    
    try:
        # 1. 加载配置和数据
        from utils.config_manager_simple import ConfigManager
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        data_loader = DataLoader(config)
        
        print("✓ 配置和数据加载器初始化成功")
        
        # 2. 验证路径配置
        print(f"\n=== 路径配置验证 ===")
        cabinet_path = config_manager.get('data_paths.cabinet_profiles')
        wiring_path = config_manager.get('data_paths.wiring_typical')
        iodb_path = config_manager.get('data_paths.iodb')
        pidb_path = config_manager.get('data_paths.pidb')
        
        print(f"✓ 机柜配置路径: {Path(cabinet_path).name}")
        print(f"✓ 典型回路路径: {Path(wiring_path).name}")
        print(f"✓ IODB路径: {Path(iodb_path).name}")
        print(f"✓ PIDB路径: {Path(pidb_path).name}")
        
        # 3. 加载所有数据
        print(f"\n=== 数据加载测试 ===")
        cabinet_profiles = data_loader.load_cabinet_profiles()
        wiring_typicals = data_loader.load_wiring_typicals()
        
        print(f"✓ 机柜配置: {len(cabinet_profiles)}个")
        print(f"✓ 典型回路: {len(wiring_typicals)}个")
        
        # 验证机柜配置内容
        for name, profile in list(cabinet_profiles.items())[:2]:
            rails_count = len(profile.get('rails', []))
            racks_count = len(profile.get('racks', []))
            print(f"  - {name}: {rails_count}导轨, {racks_count}机架")
        
        # 验证典型回路内容
        for name, typical in list(wiring_typicals.items())[:2]:
            components_count = len(typical.get('components', []))
            print(f"  - {name}: {components_count}组件")
        
        # 4. 加载IODB数据
        iodb_path_obj = Path(iodb_path)
        iodb_files = list(iodb_path_obj.glob("*.xlsx"))
        if not iodb_files:
            print("❌ 未找到IODB文件")
            return False
        
        iodb_data = data_loader.load_iodb_data(str(iodb_files[0]))
        print(f"✓ IODB数据: {len(iodb_data.get('io_points', []))}个I/O点, {len(iodb_data.get('cables', {}))}条电缆")
        
        # 5. 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            {},
            cabinet_profiles,
            wiring_typicals
        )
        
        print(f"✓ 数据模型: {len(models['io_points'])}点, {len(models['cables'])}缆, {len(models['cabinets'])}柜")
        
        # 6. 执行分配
        print(f"\n=== I/O点分配测试 ===")
        allocator = IOAllocator(config)
        
        allocation_result = allocator.allocate_io_points(
            models['cables'],
            models['cabinets'],
            {wt.name: {'components': []} for wt in models['wiring_typicals']}
        )
        
        # 7. 验证分配结果
        print(f"✓ 分配执行完成")
        
        # 分析分配点类型
        regular_points = 0
        spare_points = 0
        for point in allocation_result.allocated_points:
            if hasattr(point, 'is_spare') and point.is_spare:
                spare_points += 1
            else:
                regular_points += 1
        
        failed_points = len(allocation_result.failed_points)
        
        print(f"  - 常规I/O点分配: {regular_points}")
        print(f"  - Spare点生成: {spare_points}")
        print(f"  - 分配失败: {failed_points}")
        print(f"  - 总分配点数: {regular_points + spare_points}")
        
        # 8. 验证统计数据
        print(f"\n=== 统计数据验证 ===")
        if allocation_result.summary:
            summary = allocation_result.summary
            print(f"✓ 统计摘要:")
            print(f"  - 总I/O点数: {summary.get('total_points', 0)}")
            print(f"  - 分配成功: {summary.get('allocated_count', 0)}")
            print(f"  - 分配失败: {summary.get('failed_count', 0)}")
            print(f"  - 成功率: {summary.get('success_rate', 0):.1f}%")
            print(f"  - Spare点数: {summary.get('spare_points_generated', 0)}")
            
            # 验证数据一致性
            expected_regular = len(models['io_points'])
            actual_regular = summary.get('allocated_count', 0)
            actual_spare = summary.get('spare_points_generated', 0)
            
            print(f"\n✓ 数据一致性检查:")
            print(f"  - 期望常规点: {expected_regular}")
            print(f"  - 实际常规点: {actual_regular}")
            print(f"  - 实际spare点: {actual_spare}")
            print(f"  - 常规点一致性: {'✓' if expected_regular == actual_regular else '❌'}")
            print(f"  - 总分配点数: {regular_points + spare_points}")
        
        # 9. 验证成功率计算
        print(f"\n=== 成功率计算验证 ===")
        if allocation_result.summary:
            summary = allocation_result.summary
            total_original = summary.get('total_points', 0)
            allocated_original = summary.get('allocated_count', 0)
            
            if total_original > 0:
                calculated_rate = (allocated_original / total_original) * 100
                reported_rate = summary.get('success_rate', 0)
                
                print(f"✓ 成功率计算:")
                print(f"  - 原始I/O点总数: {total_original}")
                print(f"  - 原始I/O点分配成功: {allocated_original}")
                print(f"  - 计算成功率: {calculated_rate:.1f}%")
                print(f"  - 报告成功率: {reported_rate:.1f}%")
                print(f"  - 计算正确性: {'✓' if abs(calculated_rate - reported_rate) < 0.1 else '❌'}")
        
        # 10. 模拟GUI显示
        print(f"\n=== GUI显示模拟 ===")
        # 模拟修复后的GUI显示逻辑
        if spare_points > 0:
            gui_status = f"I/O点分配完成: {regular_points}/{regular_points + failed_points} 常规点, {spare_points} spare点"
        else:
            gui_status = f"I/O点分配完成: {regular_points}/{regular_points + failed_points} 成功分配"
        
        print(f"✓ GUI状态消息: {gui_status}")
        
        # 11. 最终验证
        print(f"\n=== 最终验证 ===")
        all_checks_passed = True
        
        # 检查1: 数据加载正常
        if len(models['io_points']) == 0:
            print("❌ 数据加载失败")
            all_checks_passed = False
        else:
            print("✓ 数据加载正常")
        
        # 检查2: 分配算法正常
        if regular_points != len(models['io_points']):
            print("❌ 分配算法异常")
            all_checks_passed = False
        else:
            print("✓ 分配算法正常")
        
        # 检查3: 统计计算正确
        if allocation_result.summary:
            summary = allocation_result.summary
            if summary.get('success_rate', 0) > 100:
                print("❌ 成功率计算异常")
                all_checks_passed = False
            else:
                print("✓ 成功率计算正确")
        
        # 检查4: Spare点生成正常
        if spare_points > 0:
            print("✓ Spare点生成正常")
        else:
            print("⚠️ 未生成Spare点（可能是配置问题）")
        
        if all_checks_passed:
            print(f"\n🎉 所有检查通过！I/O点分配系统工作正常。")
        else:
            print(f"\n❌ 部分检查失败，请检查相关问题。")
        
        return all_checks_passed
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_allocation_system()
    if success:
        print("\n✅ 系统测试完成，所有功能正常！")
    else:
        print("\n❌ 系统测试发现问题，需要进一步检查。")
