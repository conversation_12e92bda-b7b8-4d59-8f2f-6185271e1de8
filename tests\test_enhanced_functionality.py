"""
增强功能综合测试
测试所有新增的功能模块
"""

import sys
import os
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_report_engine():
    """测试报表生成引擎"""
    print("1. 测试报表生成引擎...")
    
    from core.report_engine import ReportEngine, ReportRequest, ReportTemplate
    from core.data_models import IOPoint, SignalType, AllocationResult
    
    # 创建测试配置
    config = {
        'report_settings': {
            'template_dir': 'test_templates'
        }
    }
    
    # 初始化报表引擎
    engine = ReportEngine(config)
    
    # 测试获取模板
    templates = engine.get_available_templates()
    assert len(templates) > 0, "应该有可用的模板"
    print(f"   ✅ 加载了 {len(templates)} 个报表模板")
    
    # 创建测试数据
    test_points = [
        IOPoint(tag="FT001", signal_type=SignalType.AI, description="Flow transmitter"),
        IOPoint(tag="PT001", signal_type=SignalType.AI, description="Pressure transmitter"),
        IOPoint(tag="SPARE_1", signal_type=SignalType.DI, description="-", is_spare=True)
    ]
    
    allocation_result = AllocationResult()
    allocation_result.allocated_points = test_points
    allocation_result.success = True
    
    # 创建报表请求
    with tempfile.TemporaryDirectory() as temp_dir:
        output_path = os.path.join(temp_dir, "test_report.csv")
        
        request = ReportRequest(
            template_id="io_allocation",
            output_path=output_path,
            data_sources={'allocation_result': allocation_result}
        )
        
        # 生成报表
        result = engine.generate_report(request)
        
        assert result.success, f"报表生成失败: {result.errors}"
        print(f"   ✅ 报表生成成功: {result.output_path}")


def test_naming_engine():
    """测试命名引擎"""
    print("\n2. 测试命名引擎...")
    
    from core.naming_engine import NamingEngine, NamingContext, NamingElementType
    from core.data_models import SignalType
    
    # 创建命名引擎
    config = {}
    engine = NamingEngine(config)
    
    # 测试机柜命名
    context = NamingContext(
        system="SIS",
        location="CR",
        sequence_number=1
    )
    
    result = engine.generate_name(NamingElementType.CABINET, context)
    
    assert result.success, f"命名生成失败: {result.warnings}"
    assert result.generated_name, "应该生成名称"
    print(f"   ✅ 生成机柜名称: {result.generated_name}")
    
    # 测试冲突解决
    engine.register_existing_name(NamingElementType.CABINET, result.generated_name)
    
    result2 = engine.generate_name(NamingElementType.CABINET, context)
    assert result2.success, "冲突解决失败"
    assert result2.generated_name != result.generated_name, "应该生成不同的名称"
    print(f"   ✅ 冲突解决成功: {result2.generated_name}")


def test_pidb_enhanced_reader():
    """测试PIDB增强读取器"""
    print("\n3. 测试PIDB增强读取器...")
    
    from core.pidb_enhanced_reader import PidbEnhancedReader
    import pandas as pd
    
    # 创建测试Excel文件
    with tempfile.TemporaryDirectory() as temp_dir:
        test_file = os.path.join(temp_dir, "test_pidb.xlsx")
        
        # 创建测试数据
        test_data = {
            'tagname': ['FT001', 'PT001', 'XV001'],
            'description': ['Flow transmitter', 'Pressure transmitter', 'Control valve'],
            'signal_type': ['AI', 'AI', 'DO'],
            'system': ['Process', 'Process', 'Control'],
            'location': ['Field', 'Field', 'Field']
        }
        
        df = pd.DataFrame(test_data)
        df.to_excel(test_file, index=False)
        
        # 测试读取
        config = {}
        reader = PidbEnhancedReader(config)
        
        result = reader.read_pidb_file(test_file)
        
        assert result.success, f"PIDB读取失败: {result.errors}"
        assert len(result.io_points) == 3, f"应该读取3个I/O点，实际: {len(result.io_points)}"
        print(f"   ✅ PIDB读取成功，处理了 {len(result.io_points)} 个I/O点")


def test_advanced_allocators():
    """测试高级分配策略"""
    print("\n4. 测试高级分配策略...")
    
    from core.advanced_allocators import PriorityBasedAllocator, LoadBalancingAllocator
    from core.data_models import IOPoint, SignalType
    
    # 创建测试数据
    test_points = [
        IOPoint(tag="FT001", signal_type=SignalType.AI, description="Flow transmitter"),
        IOPoint(tag="PT001", signal_type=SignalType.AI, description="Pressure transmitter"),
        IOPoint(tag="LS001", signal_type=SignalType.DI, description="Level switch")
    ]
    
    test_resources = {
        'cabinets': [
            {'name': 'Cabinet01', 'max_capacity': 32, 'used_capacity': 10},
            {'name': 'Cabinet02', 'max_capacity': 32, 'used_capacity': 5}
        ]
    }
    
    # 测试优先级分配器
    config = {
        'allocation_priorities': {
            'AI': {'priority': 1, 'weight': 1.0},
            'DI': {'priority': 2, 'weight': 0.8}
        }
    }
    
    priority_allocator = PriorityBasedAllocator(config)
    result = priority_allocator.allocate(test_points, test_resources)
    
    assert result.success, f"优先级分配失败: {result.errors}"
    print(f"   ✅ 优先级分配成功，分配了 {len(result.allocated_points)} 个点")
    
    # 测试负载均衡分配器
    load_config = {
        'load_balancing': {'threshold': 0.8}
    }
    
    load_allocator = LoadBalancingAllocator(load_config)
    result2 = load_allocator.allocate(test_points, test_resources)
    
    assert result2.success, f"负载均衡分配失败: {result2.errors}"
    print(f"   ✅ 负载均衡分配成功，分配了 {len(result2.allocated_points)} 个点")


def test_constraint_solver():
    """测试约束求解器"""
    print("\n5. 测试约束求解器...")
    
    from core.constraint_solver import ConstraintSolver, Constraint, ConstraintType, ConstraintOperator
    from core.data_models import IOPoint, SignalType
    
    # 创建约束求解器
    config = {
        'constraint_solver': {
            'max_iterations': 100,
            'tolerance': 1e-6
        }
    }
    
    solver = ConstraintSolver(config)
    
    # 创建测试数据
    test_points = [
        IOPoint(tag="FT001", signal_type=SignalType.AI),
        IOPoint(tag="PT001", signal_type=SignalType.AI)
    ]
    
    test_resources = {
        'cabinets': [
            {'name': 'Cabinet01', 'capacity': 32}
        ]
    }
    
    # 创建约束
    constraints = solver.create_default_constraints(test_points, test_resources)
    
    # 求解
    solution = solver.solve(test_points, test_resources, constraints)
    
    assert len(solution.variables) > 0, "应该创建决策变量"
    print(f"   ✅ 约束求解完成，创建了 {len(solution.variables)} 个变量")
    print(f"   ✅ 求解状态: {'可行' if solution.is_feasible else '不可行'}")


def test_project_lifecycle_manager():
    """测试项目生命周期管理器"""
    print("\n6. 测试项目生命周期管理器...")
    
    from core.project_lifecycle_manager import ProjectLifecycleManager, ProjectPhase
    
    # 创建项目管理器
    config = {
        'project_settings': {
            'template_dir': 'test_templates'
        }
    }
    
    manager = ProjectLifecycleManager(config)
    
    # 测试获取模板
    templates = manager.get_available_templates()
    assert len(templates) > 0, "应该有可用的项目模板"
    print(f"   ✅ 加载了 {len(templates)} 个项目模板")
    
    # 测试创建项目
    with tempfile.TemporaryDirectory() as temp_dir:
        project_path = os.path.join(temp_dir, "test_project")
        
        success = manager.create_project(
            project_path=project_path,
            template_id="standard_io_allocation",
            project_name="测试项目",
            description="这是一个测试项目"
        )
        
        assert success, "项目创建失败"
        assert os.path.exists(project_path), "项目目录应该存在"
        print(f"   ✅ 项目创建成功: {project_path}")
        
        # 测试加载项目元数据
        metadata = manager.load_project_metadata(project_path)
        assert metadata is not None, "应该能加载项目元数据"
        assert metadata.name == "测试项目", "项目名称应该正确"
        print(f"   ✅ 项目元数据加载成功: {metadata.name}")
        
        # 测试更新项目阶段
        success = manager.update_project_phase(project_path, ProjectPhase.DATA_IMPORT)
        assert success, "项目阶段更新失败"
        print(f"   ✅ 项目阶段更新成功")


def test_performance_monitor():
    """测试性能监控系统"""
    print("\n7. 测试性能监控系统...")
    
    from core.performance_monitor import PerformanceMonitor, PerformanceMetric
    import time
    
    # 创建性能监控器
    config = {
        'performance_monitoring': {
            'collection_interval': 0.1,  # 快速测试
            'alert_thresholds': {
                'cpu_percent': 80.0,
                'memory_percent': 85.0
            }
        }
    }
    
    monitor = PerformanceMonitor(config)
    
    # 启动监控
    monitor.start_monitoring()
    
    # 等待收集一些数据
    time.sleep(0.5)
    
    # 记录分配性能
    monitor.record_allocation_performance(
        total_points=100,
        allocation_time=2.5,
        success_rate=0.95,
        memory_peak_mb=150.0
    )
    
    # 获取性能摘要
    summary = monitor.get_performance_summary()
    
    assert summary['monitoring_status'] == 'active', "监控应该处于活动状态"
    assert summary['allocation_records'] > 0, "应该有分配记录"
    print(f"   ✅ 性能监控正常，收集了 {summary['metrics_collected']} 个指标")
    
    # 停止监控
    monitor.stop_monitoring()
    print(f"   ✅ 性能监控已停止")


def test_integration():
    """集成测试"""
    print("\n8. 集成测试...")
    
    from core.allocator import IOAllocator
    from core.data_models import IOPoint, SignalType
    from utils.config_manager_simple import ConfigManager
    
    # 加载配置
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    # 创建分配器（应该包含所有增强功能）
    allocator = IOAllocator(config)
    
    # 验证增强功能已集成
    assert hasattr(allocator, 'spare_manager'), "分配器应该包含spare管理器"
    print(f"   ✅ 分配器集成验证通过")
    
    # 创建测试数据
    test_cables = [{
        'name': 'TEST_CABLE',
        'pair_size': 4,
        'io_points': [
            IOPoint(tag="FT001", signal_type=SignalType.AI, pair_number=1, wiring_typical="AI IS BABP"),
            IOPoint(tag="PT001", signal_type=SignalType.AI, pair_number=2, wiring_typical="AI IS BABP")
        ]
    }]
    
    test_cabinets = [{
        'name': 'TEST_CABINET',
        'rails': [{'name': 'Rail1', 'width': 500.0}]
    }]
    
    # 执行分配
    result = allocator.allocate_io_points(
        cables=test_cables,
        cabinets=test_cabinets,
        wiring_typicals={}
    )
    
    assert result.success, f"集成分配失败: {result.errors}"
    
    # 验证spare点生成
    spare_points = [p for p in result.allocated_points if hasattr(p, 'is_spare') and p.is_spare]
    assert len(spare_points) > 0, "应该生成spare点"
    print(f"   ✅ 集成测试通过，生成了 {len(spare_points)} 个spare点")


def main():
    """主测试函数"""
    print("=" * 60)
    print("增强功能综合测试")
    print("=" * 60)
    
    try:
        test_report_engine()
        test_naming_engine()
        test_pidb_enhanced_reader()
        test_advanced_allocators()
        test_constraint_solver()
        test_project_lifecycle_manager()
        test_performance_monitor()
        test_integration()
        
        print("\n" + "=" * 60)
        print("🎉 所有增强功能测试通过！")
        print("=" * 60)
        
        print("\n✅ 已验证的增强功能:")
        print("   • 报表生成引擎 - 统一报表生成和模板管理")
        print("   • 命名引擎系统 - 智能命名和冲突解决")
        print("   • PIDB增强读取器 - 增强的数据读取和验证")
        print("   • 高级分配策略 - 优先级和负载均衡分配")
        print("   • 约束求解器 - 复杂约束求解和优化")
        print("   • 项目生命周期管理 - 项目模板和版本控制")
        print("   • 性能监控系统 - 实时性能监控和分析")
        print("   • 系统集成 - 所有功能无缝集成")
        
        print("\n🚀 系统功能特性:")
        print("   • 模块化架构设计，易于扩展和维护")
        print("   • 完整的错误处理和日志记录")
        print("   • 高性能的数据处理和分配算法")
        print("   • 灵活的配置管理和模板系统")
        print("   • 实时性能监控和诊断工具")
        print("   • 智能命名和冲突解决机制")
        print("   • 多格式报表生成和导出")
        print("   • 项目生命周期和版本管理")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = main()
    if not success:
        sys.exit(1)
