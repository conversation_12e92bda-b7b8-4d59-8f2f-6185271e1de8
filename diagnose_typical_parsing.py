#!/usr/bin/env python3
"""
诊断典型回路解析问题
检查实际解析出的器件定义
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def diagnose_typical_parsing():
    """诊断典型回路解析"""
    print("=== 诊断典型回路解析 ===")
    
    try:
        from core.wiring_typical_parser import WiringTypicalManager
        
        # 加载典型回路
        manager = WiringTypicalManager("01C_Wiring Typical")
        typicals = manager.load_all_typicals()
        
        # 检查问题典型回路
        problem_typical_names = ["DI NIS N", "DI NIS REMOTE", "DI NIS MCC RY"]
        
        for typical_name in problem_typical_names:
            typical_def = typicals.get(typical_name)
            if not typical_def:
                print(f"❌ 未找到典型回路: {typical_name}")
                continue
            
            print(f"\n典型回路: {typical_name}")
            print(f"  机柜类型要求: {getattr(typical_def, 'required_cabinet_type', 'Unknown')}")
            
            marshalling_components = getattr(typical_def, 'marshalling_components', [])
            print(f"  Marshalling器件数: {len(marshalling_components)}")
            
            for i, comp in enumerate(marshalling_components, 1):
                comp_name = getattr(comp, 'name', 'Unknown')
                comp_type = getattr(comp, 'component_type', None)
                comp_type_value = comp_type.value if comp_type else 'Unknown'
                part_number = getattr(comp, 'part_number', 'Unknown')
                width = getattr(comp, 'width', None)
                length = getattr(comp, 'length', None)
                space_req = getattr(comp, 'space_requirement', 0)
                
                print(f"    器件 {i}: {comp_name}")
                print(f"      类型: {comp_type_value}")
                print(f"      型号: {part_number}")
                print(f"      尺寸: width={width}, length={length}")
                print(f"      空间需求: {space_req}mm")
                
                # 检查是否是问题器件
                if comp_name in ['FTB1', 'FTB2', '3000510-380C1R']:
                    print(f"      ⚠️  这是问题器件！")
                    
                    # 详细检查器件属性
                    properties = getattr(comp, 'properties', {})
                    print(f"      属性: {properties}")
        
        return True
        
    except Exception as e:
        print(f"❌ 典型回路解析诊断失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_allocation_scenario():
    """测试真实分配场景"""
    print("\n=== 测试真实分配场景 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        from core.data_models import IOPoint, SignalType
        
        # 创建配置
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        
        # 加载PIDB数据
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        data_loader = DataLoader(config)
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        # 创建机柜配置
        cabinet_profiles = {
            'PPG REL': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_REL_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            }
        }
        
        # 创建问题I/O点
        iodb_data = {
            'io_points': [
                IOPoint(
                    tag="1105-HZ-004-3",
                    signal_type=SignalType.DI,
                    description="Test DI Point",
                    wiring_typical="DI NIS N",  # 使用问题典型回路
                    location="Field Area 1105"
                )
            ],
            'cables': {
                'Cable_Problem': {
                    'name': 'Cable_Problem',
                    'io_points': [
                        IOPoint(
                            tag="1105-HZ-004-3",
                            signal_type=SignalType.DI,
                            description="Test DI Point",
                            wiring_typical="DI NIS N",
                            location="Field Area 1105"
                        )
                    ]
                }
            }
        }
        
        # 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            {}
        )
        
        cabinets = models.get('cabinets', [])
        cables = models.get('cables', [])
        
        print(f"测试场景:")
        print(f"  I/O点: 1105-HZ-004-3")
        print(f"  典型回路: DI NIS N")
        print(f"  可用机柜: {len(cabinets)} 个")
        
        # 显示机柜信息
        for cabinet in cabinets:
            name = cabinet.get('name', 'Unknown')
            cabinet_type = cabinet.get('type', 'Unknown')
            rails = cabinet.get('rails', [])
            print(f"    - {name} ({cabinet_type}): {len(rails)} 个导轨")
            
            for rail in rails:
                rail_name = rail.get('name', 'Unknown')
                supported_types = []
                for i in range(1, 11):
                    part_type_key = f'PartType{i:02d}'
                    part_type_value = rail.get(part_type_key)
                    if part_type_value:
                        supported_types.append(part_type_value)
                print(f"      导轨 {rail_name}: {supported_types}")
        
        # 执行分配
        allocator = IOAllocator(config)
        result = allocator.allocate_io_points(cables, cabinets, {})
        
        print(f"\n分配结果:")
        print(f"  成功: {result.success}")
        print(f"  成功分配: {len(result.allocated_points)}")
        print(f"  失败分配: {len(result.failed_points)}")
        
        if result.errors:
            print(f"  错误信息:")
            for error in result.errors:
                print(f"    - {error}")
        
        if result.allocated_points:
            print(f"  成功分配的I/O点:")
            for io_point in result.allocated_points:
                cabinet_name = getattr(io_point, 'cabinet', 'Unknown')
                print(f"    - {io_point.tag} -> {cabinet_name}")
        
        if result.failed_points:
            print(f"  失败分配的I/O点:")
            for io_point in result.failed_points:
                print(f"    - {io_point.tag}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 真实分配场景测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始典型回路解析问题诊断...")
    
    test_results = []
    
    # 执行测试
    test_results.append(diagnose_typical_parsing())
    test_results.append(test_real_allocation_scenario())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 诊断结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("✅ 典型回路解析正常")
        print("\n🔍 如果仍有兼容性问题，可能的原因:")
        print("1. 器件尺寸信息缺失导致空间需求为0")
        print("2. 器件类型映射不正确")
        print("3. 机柜轮询过程中的数据传递问题")
        print("4. 其他运行时条件不同")
    else:
        print("⚠️  发现典型回路解析问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
