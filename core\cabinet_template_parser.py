#!/usr/bin/env python3
"""
机柜模板解析器
从XML文件中解析机柜模板的完整导轨配置
"""

import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Dict, List, Any, Optional
import logging


class CabinetTemplateParser:
    """机柜模板解析器"""
    
    def __init__(self, template_dir: str = "01B_Cabinet Templates"):
        """
        初始化机柜模板解析器
        
        Args:
            template_dir: 机柜模板目录路径
        """
        self.template_dir = Path(template_dir)
        self.logger = logging.getLogger(__name__)
        
    def parse_cabinet_template(self, template_name: str) -> Dict[str, Any]:
        """
        解析机柜模板文件
        
        Args:
            template_name: 模板名称（如 'PPG BAR'）
            
        Returns:
            机柜模板配置字典
        """
        template_file = self.template_dir / f"{template_name}.xml"
        
        if not template_file.exists():
            self.logger.error(f"机柜模板文件不存在: {template_file}")
            return {}
        
        try:
            tree = ET.parse(template_file)
            root = tree.getroot()
            
            # 解析基本信息
            cabinet_config = {
                'name': template_name,
                'type': self._extract_cabinet_type(root),
                'rails': self._extract_rails(root),
                'properties': self._extract_properties(root)
            }
            
            self.logger.info(f"成功解析机柜模板 {template_name}: {len(cabinet_config['rails'])} 根导轨")
            return cabinet_config
            
        except Exception as e:
            self.logger.error(f"解析机柜模板失败 {template_file}: {e}")
            return {}
    
    def _extract_cabinet_type(self, root: ET.Element) -> str:
        """提取机柜类型"""
        # 从ProfileSignature中提取Type
        type_signature = root.find(".//ProfileSignature[@Name='Type']")
        if type_signature is not None:
            cabinet_type = type_signature.get('Value', '')
            if cabinet_type:
                return cabinet_type
        
        # 从根元素的Type属性推断
        root_type = root.get('Type', '')
        if 'MAR' in root_type or 'Marshalling' in root_type:
            return 'Marshalling'
        elif 'SYS' in root_type or 'System' in root_type:
            return 'System'
        else:
            return 'Mixed'
    
    def _extract_rails(self, root: ET.Element) -> List[Dict[str, Any]]:
        """提取导轨配置"""
        rails = []
        
        # 查找所有导轨组件
        rail_components = root.findall(".//ProfileComponent[@Mapping='Rail']")
        
        for rail_elem in rail_components:
            rail_config = self._parse_rail_component(rail_elem)
            if rail_config:
                rails.append(rail_config)
        
        return rails
    
    def _parse_rail_component(self, rail_elem: ET.Element) -> Optional[Dict[str, Any]]:
        """解析单个导轨组件"""
        try:
            rail_name = rail_elem.get('Name', '')
            if not rail_name:
                return None
            
            # 提取导轨属性
            properties = {}
            for prop_elem in rail_elem.findall(".//ProfileProperty"):
                prop_name = prop_elem.get('Name', '')
                prop_value = prop_elem.get('Value', '')
                if prop_name and prop_value:
                    properties[prop_name] = prop_value
            
            # 构建导轨配置
            rail_config = {
                'name': rail_name,
                'position': properties.get('Position', rail_name),
                'length': float(properties.get('Length', '2000')),
                'io_type': properties.get('IOType', 'Mixed'),
                'intrinsic': properties.get('Intrinsic', 'NIS'),
                'voltage_level': int(properties.get('VoltageLevel', '24')),
                'reserved_from': float(properties.get('ReservedFrom', '0')),
                'reserved_to': float(properties.get('ReservedTo', '0'))
            }
            
            # 提取支持的器件类型
            part_type_index = 1
            while True:
                part_type_key = f'PartType{part_type_index:02d}'
                if part_type_key in properties:
                    rail_config[part_type_key] = properties[part_type_key]
                    part_type_index += 1
                else:
                    break
            
            # 提取支持的器件型号
            part_num_index = 1
            while True:
                part_num_key = f'PartNum{part_num_index:02d}'
                if part_num_key in properties:
                    rail_config[part_num_key] = properties[part_num_key]
                    part_num_index += 1
                else:
                    break
            
            return rail_config
            
        except Exception as e:
            self.logger.warning(f"解析导轨组件失败: {e}")
            return None
    
    def _extract_properties(self, root: ET.Element) -> Dict[str, Any]:
        """提取机柜基本属性"""
        properties = {}
        
        for prop_elem in root.findall(".//ProfileProperty"):
            prop_name = prop_elem.get('Name', '')
            prop_value = prop_elem.get('Value', '')
            prop_units = prop_elem.get('Units', '')
            
            if prop_name and prop_value:
                # 尝试转换数值类型
                try:
                    if prop_units in ['mm', 'Vdc', 'kgs']:
                        properties[prop_name] = float(prop_value)
                    else:
                        properties[prop_name] = prop_value
                except ValueError:
                    properties[prop_name] = prop_value
        
        return properties
    
    def load_all_cabinet_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载所有机柜模板"""
        templates = {}
        
        if not self.template_dir.exists():
            self.logger.error(f"机柜模板目录不存在: {self.template_dir}")
            return templates
        
        # 查找所有XML文件
        for xml_file in self.template_dir.glob("*.xml"):
            template_name = xml_file.stem
            template_config = self.parse_cabinet_template(template_name)
            if template_config:
                templates[template_name] = template_config
        
        self.logger.info(f"加载了 {len(templates)} 个机柜模板")
        return templates


def main():
    """测试机柜模板解析器"""
    parser = CabinetTemplateParser()
    
    # 测试PPG BAR模板
    ppg_bar_config = parser.parse_cabinet_template("PPG BAR")
    
    print(f"PPG BAR 机柜配置:")
    print(f"  类型: {ppg_bar_config.get('type', 'Unknown')}")
    print(f"  导轨数量: {len(ppg_bar_config.get('rails', []))}")
    
    for i, rail in enumerate(ppg_bar_config.get('rails', []), 1):
        print(f"  导轨 {i}: {rail['name']}")
        print(f"    位置: {rail['position']}")
        print(f"    长度: {rail['length']}mm")
        print(f"    I/O类型: {rail['io_type']}")
        print(f"    本安类型: {rail['intrinsic']}")
        
        # 显示支持的器件类型
        part_types = []
        for j in range(1, 11):
            part_type_key = f'PartType{j:02d}'
            if part_type_key in rail:
                part_types.append(rail[part_type_key])
        print(f"    支持器件: {part_types}")


if __name__ == "__main__":
    main()
