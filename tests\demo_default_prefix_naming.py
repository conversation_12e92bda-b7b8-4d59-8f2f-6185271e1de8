"""
默认前缀命名功能演示
展示使用原有默认前缀的灵活命名配置
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def demo_default_prefixes():
    """演示默认前缀功能"""
    print("=== 默认前缀功能演示 ===\n")
    
    from gui.simple_naming_dialog import NamingElement
    
    print("1. 各器件类型的默认前缀:")
    
    # 默认前缀映射
    default_prefixes = {
        'barrier': 'BA',
        'relay': 'RY',
        'isolator': 'ISL',
        'surge_protector': 'SP',
        'terminal_block': 'TB',
        'tr_terminal': 'TR'
    }
    
    device_names = {
        'barrier': '安全栅',
        'relay': '继电器',
        'isolator': '隔离器',
        'surge_protector': '防雷栅',
        'terminal_block': '端子排',
        'tr_terminal': 'TR端子排'
    }
    
    for device_type, prefix in default_prefixes.items():
        device_name = device_names[device_type]
        print(f"   {device_name}: {prefix}")
    
    print()

def demo_default_naming_rules():
    """演示默认命名规则"""
    print("=== 默认命名规则演示 ===\n")
    
    from gui.simple_naming_dialog import NamingElement
    
    print("2. 使用默认前缀的命名规则:")
    
    # 模拟各器件类型的默认命名规则
    naming_examples = [
        {
            'device': '安全栅',
            'prefix': 'BA',
            'elements': ['固定文本(BA)', '机架编号', '槽位编号', '通道号'],
            'example': 'BA1305'
        },
        {
            'device': '继电器',
            'prefix': 'RY',
            'elements': ['固定文本(RY)', '机架编号', '槽位编号', '通道号'],
            'example': 'RY1305'
        },
        {
            'device': '隔离器',
            'prefix': 'ISL',
            'elements': ['固定文本(ISL)', '机架编号', '槽位编号', '通道号'],
            'example': 'ISL1305'
        },
        {
            'device': '防雷栅',
            'prefix': 'SP',
            'elements': ['固定文本(SP)', '机架编号', '槽位编号', '通道号'],
            'example': 'SP1305'
        },
        {
            'device': '端子排',
            'prefix': 'TB',
            'elements': ['固定文本(TB)', '机柜编号', '导轨号', '器件编号'],
            'example': 'TB02F03'
        },
        {
            'device': 'TR端子排',
            'prefix': 'TR',
            'elements': ['固定文本(TR)', 'ETP名'],
            'example': 'TRR1S3U'
        }
    ]
    
    for example in naming_examples:
        print(f"   {example['device']}:")
        print(f"     规则: {' + '.join(example['elements'])}")
        print(f"     示例: {example['example']}")
        print()

def demo_editable_fixed_text():
    """演示可编辑固定文本"""
    print("=== 可编辑固定文本演示 ===\n")
    
    from gui.simple_naming_dialog import NamingElement
    
    print("3. 固定文本编辑功能:")
    
    # 演示固定文本的编辑过程
    scenarios = [
        {
            'description': '默认安全栅前缀',
            'original': 'BA',
            'modified': 'SAFETY_BARRIER',
            'example_before': 'BA1305',
            'example_after': 'SAFETY_BARRIER1305'
        },
        {
            'description': '添加分隔符',
            'original': 'RY',
            'modified': 'RY_',
            'example_before': 'RY1305',
            'example_after': 'RY_1305'
        },
        {
            'description': '自定义端子排前缀',
            'original': 'TB',
            'modified': 'TERMINAL',
            'example_before': 'TB02F03',
            'example_after': 'TERMINAL02F03'
        }
    ]
    
    for scenario in scenarios:
        print(f"   {scenario['description']}:")
        print(f"     默认: {scenario['original']} -> {scenario['example_before']}")
        print(f"     修改: {scenario['modified']} -> {scenario['example_after']}")
        print()

def demo_complex_naming_combinations():
    """演示复杂命名组合"""
    print("=== 复杂命名组合演示 ===\n")
    
    from gui.simple_naming_dialog import NamingElement
    
    print("4. 复杂命名规则组合:")
    
    # 演示复杂的命名组合
    complex_examples = [
        {
            'description': '带项目前缀的安全栅',
            'elements': ['固定文本(PROJ1)', '固定文本(_)', '固定文本(BA)', '机架编号', '槽位编号', '通道号'],
            'example': 'PROJ1_BA1305'
        },
        {
            'description': '包含Tag名的继电器',
            'elements': ['Tag名', '固定文本(_)', '固定文本(RY)', '固定文本(_)', '机架编号'],
            'example': 'AI_TEMP_001_RY_1'
        },
        {
            'description': '详细机柜信息的端子排',
            'elements': ['固定文本(TB)', '固定文本(_)', '机柜名', '固定文本(_)', '导轨号', '器件编号'],
            'example': 'TB_1103-SIS-SYS-101_F03'
        },
        {
            'description': '自定义ETP后缀',
            'elements': ['固定文本(ETP)', '固定文本(_)', 'ETP名', '固定文本(_)', 'ETP后缀(TOP)'],
            'example': 'ETP_R1S3_TOP'
        },
        {
            'description': '信号类型标识',
            'elements': ['信号类型', '固定文本(_)', '固定文本(SENSOR)', '固定文本(_)', '机架编号', '通道号'],
            'example': 'AI_SENSOR_105'
        }
    ]
    
    for example in complex_examples:
        print(f"   {example['description']}:")
        print(f"     规则: {' + '.join(example['elements'])}")
        print(f"     示例: {example['example']}")
        print()

def demo_user_workflow():
    """演示用户工作流程"""
    print("=== 用户工作流程演示 ===\n")
    
    print("5. 完整的用户操作流程:")
    
    workflow_steps = [
        "打开分配界面，点击'命名规则'按钮",
        "选择要配置的器件类型标签页（如：安全栅）",
        "查看左侧可用命名元素列表",
        "点击'固定文本'元素，然后点击'添加 →'按钮",
        "在右侧规则列表中选择刚添加的固定文本元素",
        "在元素编辑区看到'固定文本'输入框已激活，默认值为'BA'",
        "根据需要修改固定文本内容（如改为'SAFETY'）",
        "点击'更新元素'按钮应用修改",
        "继续添加其他元素（机架编号、槽位编号、通道号）",
        "在预览区查看命名效果（如：SAFETY1305）",
        "使用上移、下移按钮调整元素顺序",
        "点击'预览所有规则'查看全局效果",
        "点击'确定'保存配置",
        "导出IO分配表时自动应用自定义命名规则"
    ]
    
    for i, step in enumerate(workflow_steps, 1):
        print(f"   {i:2d}. {step}")
    
    print()

def demo_benefits():
    """演示功能优势"""
    print("=== 功能优势演示 ===\n")
    
    print("6. 功能优势总结:")
    
    benefits = [
        {
            'category': '易用性',
            'points': [
                '添加固定文本时自动使用熟悉的默认前缀（BA、RY等）',
                '用户无需记忆或查找默认值',
                '保持与现有命名习惯的一致性'
            ]
        },
        {
            'category': '灵活性',
            'points': [
                '可以随时编辑修改固定文本内容',
                '支持复杂的前缀、分隔符、后缀组合',
                '一个器件可以使用多个固定文本元素'
            ]
        },
        {
            'category': '一致性',
            'points': [
                '固定文本对所有tag保持一致',
                '不会因为不同的IO点而变化',
                '确保命名规则的统一性'
            ]
        },
        {
            'category': '可扩展性',
            'points': [
                '支持15种不同的命名元素',
                '可以创建任意复杂的命名规则',
                '配置持久化，支持项目复用'
            ]
        }
    ]
    
    for benefit in benefits:
        print(f"   {benefit['category']}:")
        for point in benefit['points']:
            print(f"     • {point}")
        print()

def main():
    """主演示函数"""
    print("默认前缀命名功能完整演示\n")
    print("=" * 60)
    
    # 运行各个演示
    demo_default_prefixes()
    demo_default_naming_rules()
    demo_editable_fixed_text()
    demo_complex_naming_combinations()
    demo_user_workflow()
    demo_benefits()
    
    print("=" * 60)
    print("演示完成！")
    
    print("\n🎯 核心特性总结:")
    print("✓ 智能默认前缀：添加固定文本时自动使用对应器件的默认前缀")
    print("✓ 实时编辑：可以随时修改固定文本内容，立即看到效果")
    print("✓ 灵活组合：支持多个固定文本元素的复杂组合")
    print("✓ 一致性保证：固定文本对所有tag保持一致")
    print("✓ 用户友好：直观的编辑界面和实时预览")
    print("✓ 向后兼容：保持与原有命名规则的完全兼容")
    
    print("\n💡 使用建议:")
    print("1. 首次使用时，系统会自动加载默认前缀，无需手动输入")
    print("2. 根据项目需求，可以修改默认前缀为项目特定的标识")
    print("3. 利用多个固定文本元素可以创建复杂的命名模式")
    print("4. 定期备份命名配置文件，便于在不同项目间复用")

if __name__ == '__main__':
    main()
