"""
I/O点分配器
负责执行I/O点的智能分配算法
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

from core.logger import get_logger
from core.data_models import IOPoint, SignalType
from core.spare_manager import SparePointManager, SpareStrategy
from core.component_allocation_models import ComponentType


class TerminalBlockStrategy(Enum):
    """端子排分配策略"""
    ETP_FORM = "ETP_FORM"      # ETP形式
    CABLE_FORM = "CABLE_FORM"  # 电缆形式


class CardAllocationStrategy(Enum):
    """卡件分配策略"""
    PRIORITY = "PRIORITY"          # 优先级分配
    LOAD_BALANCE = "LOAD_BALANCE"  # 负载均衡


class RackPrefix(Enum):
    """机架前缀"""
    R = "R"  # R前缀
    C = "C"  # C前缀


@dataclass
class AllocationResult:
    """分配结果"""
    success: bool = False
    allocated_points: List[IOPoint] = None
    failed_points: List[IOPoint] = None
    warnings: List[str] = None
    errors: List[str] = None
    summary: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.allocated_points is None:
            self.allocated_points = []
        if self.failed_points is None:
            self.failed_points = []
        if self.warnings is None:
            self.warnings = []
        if self.errors is None:
            self.errors = []
        if self.summary is None:
            self.summary = {}

    def add_error(self, error_message: str):
        """添加错误信息"""
        self.errors.append(error_message)

    def add_warning(self, warning_message: str):
        """添加警告信息"""
        self.warnings.append(warning_message)

    @property
    def allocation_summary(self):
        """allocation_summary属性别名，指向summary"""
        return self.summary

    @allocation_summary.setter
    def allocation_summary(self, value):
        """设置allocation_summary，实际设置summary"""
        self.summary = value


class CardSlotManager:
    """卡件槽位管理器"""
    
    def __init__(self):
        """初始化卡件槽位管理器"""
        self.rack_prefix = RackPrefix.R
        self.etp_upper_suffix = "U"
        self.etp_lower_suffix = "L"
        self.allocation_strategy = CardAllocationStrategy.PRIORITY
    
    def set_rack_prefix(self, prefix: RackPrefix):
        """设置机架前缀"""
        self.rack_prefix = prefix
    
    def set_etp_suffixes(self, upper: str, lower: str):
        """设置ETP后缀"""
        self.etp_upper_suffix = upper
        self.etp_lower_suffix = lower
    
    def set_allocation_strategy(self, strategy: CardAllocationStrategy):
        """设置分配策略"""
        self.allocation_strategy = strategy


class IOAllocator:
    """I/O点分配器"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化分配器
        
        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        
        # 初始化子组件
        self.card_slot_manager = CardSlotManager()
        self.spare_manager = SparePointManager(config)

        # 获取分配设置
        self.allocation_settings = config.get('allocation_settings', {})
        self.enable_parttype_matching = self.allocation_settings.get('enable_parttype_matching', True)
        self.enable_detailed_logging = self.allocation_settings.get('enable_detailed_logging', True)
        self.max_allocation_attempts = self.allocation_settings.get('max_allocation_attempts', 1000)

        # 分配策略
        self.terminal_block_strategy = TerminalBlockStrategy.ETP_FORM
    
    def set_terminal_block_strategy(self, strategy: TerminalBlockStrategy):
        """
        设置端子排分配策略
        
        Args:
            strategy: 端子排分配策略
        """
        self.terminal_block_strategy = strategy
        self.logger.info(f"设置端子排分配策略: {strategy.value}")
    
    def allocate_io_points(self, cables: List[Dict[str, Any]], 
                          cabinets: List[Dict[str, Any]], 
                          wiring_typicals: Dict[str, Any]) -> AllocationResult:
        """
        分配I/O点
        
        Args:
            cables: 电缆列表
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典
            
        Returns:
            分配结果
        """
        self.logger.info(f"开始I/O点分配，电缆数: {len(cables)}, 机柜数: {len(cabinets)}")
        
        result = AllocationResult()
        
        try:
            # 初始化分配统计
            total_points = 0
            allocated_count = 0
            failed_count = 0
            
            # 统计总I/O点数
            for cable in cables:
                cable_points = cable.get('io_points', [])
                total_points += len(cable_points)
            
            self.logger.info(f"总计需要分配 {total_points} 个I/O点")
            
            # 执行分配算法
            for cable in cables:
                cable_result = self._allocate_cable(cable, cabinets, wiring_typicals)
                
                # 合并结果
                result.allocated_points.extend(cable_result.allocated_points)
                result.failed_points.extend(cable_result.failed_points)
                result.warnings.extend(cable_result.warnings)
                result.errors.extend(cable_result.errors)
                
                allocated_count += len(cable_result.allocated_points)
                failed_count += len(cable_result.failed_points)
            
            # 计算成功率
            success_rate = (allocated_count / total_points * 100) if total_points > 0 else 0
            
            # 生成分配摘要
            result.summary = {
                'total_points': total_points,
                'allocated_count': allocated_count,
                'failed_count': failed_count,
                'success_rate': success_rate,
                'total_cables': len(cables),
                'strategy': self.terminal_block_strategy.value
            }
            
            # 判断分配是否成功
            result.success = failed_count == 0

            # 如果基本分配成功，生成spare点
            if result.success:
                self.logger.info("开始生成spare点")
                spare_result = self.spare_manager.generate_spare_points(
                    cables=cables,
                    terminal_blocks=[],  # 暂时为空，后续可以从端子排管理器获取
                    wiring_typicals=wiring_typicals,
                    cabinets=cabinets,
                    strategy=SpareStrategy.CABLE_FORM
                )

                if spare_result.success:
                    # 将spare点添加到分配结果中
                    result.allocated_points.extend(spare_result.spare_points)
                    result.summary['spare_points_generated'] = len(spare_result.spare_points)
                    self.logger.info(f"成功生成 {len(spare_result.spare_points)} 个spare点")
                else:
                    result.warnings.extend(spare_result.errors)
                    result.summary['spare_generation_failed'] = True
                    self.logger.warning("Spare点生成失败")

            self.logger.info(f"I/O点分配完成，成功率: {success_rate:.1f}%")
            self.logger.info(f"成功分配: {allocated_count}, 失败: {failed_count}")

            return result
            
        except Exception as e:
            self.logger.error(f"I/O点分配异常: {e}")
            result.errors.append(f"分配过程异常: {e}")
            result.success = False
            return result
    
    def _allocate_cable(self, cable: Dict[str, Any], 
                       cabinets: List[Dict[str, Any]], 
                       wiring_typicals: Dict[str, Any]) -> AllocationResult:
        """
        分配单个电缆的I/O点
        
        Args:
            cable: 电缆数据
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典
            
        Returns:
            分配结果
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])
        
        self.logger.debug(f"分配电缆 {cable_name}，包含 {len(io_points)} 个I/O点")
        
        result = AllocationResult()
        
        try:
            # 实现完整的机柜轮询机制
            return self._allocate_cable_with_polling(cable, cabinets, wiring_typicals)
            
        except Exception as e:
            self.logger.error(f"分配电缆 {cable_name} 异常: {e}")
            result.errors.append(f"电缆分配异常: {e}")
            result.failed_points.extend(io_points)
            return result
    
    def _select_target_cabinet(self, cable: Dict[str, Any],
                              cabinets: List[Dict[str, Any]]) -> Optional[Dict[str, Any]]:
        """
        智能选择目标机柜

        Args:
            cable: 电缆数据
            cabinets: 机柜列表

        Returns:
            选中的机柜，如果没有合适的返回None
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])

        if not io_points:
            return None

        self.logger.debug(f"为电缆 {cable_name} 智能选择机柜，可用机柜: {len(cabinets)} 个")

        # 分析I/O点的典型回路需求
        required_cabinet_types = set()
        for io_point in io_points:
            typical_name = getattr(io_point, 'wiring_typical', '')
            if typical_name:
                # 从典型回路定义中获取机柜类型要求
                cabinet_type = self._get_cabinet_type_from_typical(typical_name)
                if cabinet_type:
                    required_cabinet_types.add(cabinet_type)
                    self.logger.debug(f"I/O点 {getattr(io_point, 'tag', 'Unknown')} 典型回路 {typical_name} 需要机柜类型: {cabinet_type}")

        if not required_cabinet_types:
            required_cabinet_types.add('Marshalling')  # 默认需求

        self.logger.debug(f"电缆 {cable_name} 需要的机柜类型: {required_cabinet_types}")

        # 按优先级选择机柜
        cabinet_priority = ['Marshalling', 'System/Marshalling', 'System']

        for required_type in required_cabinet_types:
            for priority_type in cabinet_priority:
                if required_type == 'Marshalling' and priority_type in ['Marshalling', 'System/Marshalling']:
                    # Marshalling需求可以用Marshalling或混合机柜满足
                    for cabinet in cabinets:
                        cabinet_type = cabinet.get('type', '')
                        cabinet_name = cabinet.get('name', '')
                        rails = cabinet.get('rails', [])

                        if cabinet_type == priority_type and len(rails) > 0:
                            self.logger.info(f"智能选择机柜 {cabinet_name} ({cabinet_type}) 用于电缆 {cable_name}")
                            return cabinet

                elif required_type == 'System' and priority_type in ['System', 'System/Marshalling']:
                    # System需求可以用System或混合机柜满足
                    for cabinet in cabinets:
                        cabinet_type = cabinet.get('type', '')
                        cabinet_name = cabinet.get('name', '')
                        rails = cabinet.get('rails', [])

                        if cabinet_type == priority_type and len(rails) > 0:
                            self.logger.info(f"智能选择机柜 {cabinet_name} ({cabinet_type}) 用于电缆 {cable_name}")
                            return cabinet

        # 如果没有找到完全匹配的，选择第一个可用的机柜作为回退
        for cabinet in cabinets:
            cabinet_name = cabinet.get('name', '')
            rails = cabinet.get('rails', [])

            if len(rails) > 0:
                self.logger.warning(f"未找到完全匹配的机柜，回退选择 {cabinet_name} 用于电缆 {cable_name}")
                return cabinet

        self.logger.error(f"未找到任何可用机柜用于电缆 {cable_name}")
        return None
    
    def _allocate_single_point(self, io_point: IOPoint,
                              cabinet_dict: Dict[str, Any],
                              wiring_typicals: Dict[str, Any],
                              all_cabinets: Optional[List[Dict[str, Any]]] = None) -> bool:
        """
        基于典型回路的多器件分配（重新设计）

        Args:
            io_point: I/O点对象
            cabinet_dict: 首选机柜字典数据
            wiring_typicals: 典型回路字典
            all_cabinets: 所有可用机柜列表，用于智能机柜选择

        Returns:
            分配是否成功
        """
        try:
            from core.component_allocation_engine import ComponentAllocationEngine
            from core.pidb_cabinet_loader import PIDBCabinetLoader

            # 使用新的多器件分配引擎
            allocation_engine = ComponentAllocationEngine(self.config)

            # 将机柜字典转换为ActualCabinet对象
            actual_cabinet = self._convert_dict_to_actual_cabinet(cabinet_dict)
            if not actual_cabinet:
                self.logger.error(f"[I/O点: {io_point.tag}] 无法转换机柜字典为ActualCabinet对象: {cabinet_dict.get('name', 'Unknown')}")
                return False

            # 准备可用机柜列表（如果提供）
            available_cabinets = None
            if all_cabinets:
                available_cabinets = []
                for cabinet_dict in all_cabinets:
                    converted_cabinet = self._convert_dict_to_actual_cabinet(cabinet_dict)
                    if converted_cabinet:
                        available_cabinets.append(converted_cabinet)

                self.logger.debug(f"[I/O点: {io_point.tag}] 准备了 {len(available_cabinets)} 个可用机柜用于智能选择")

            # 执行多器件分配
            allocation_result = allocation_engine.allocate_io_point_components(
                io_point, actual_cabinet, wiring_typicals, available_cabinets
            )

            if allocation_result.success:
                # 更新I/O点的分配信息
                self._update_io_point_allocation_info(io_point, allocation_result)

                self.logger.info(f"[I/O点: {io_point.tag}] 多器件分配成功: "
                               f"{len(allocation_result.component_allocations)}个器件")
                return True
            else:
                self.logger.warning(f"[I/O点: {io_point.tag}] 多器件分配失败: "
                                  f"{', '.join(allocation_result.errors)}")
                return False

        except Exception as e:
            self.logger.error(f"[I/O点: {io_point.tag}] 分配异常: {e}")
            return False

    def _update_io_point_allocation_info(self, io_point: IOPoint,
                                       allocation_result) -> None:
        """
        更新I/O点的分配信息

        Args:
            io_point: I/O点对象
            allocation_result: 多器件分配结果
        """
        if not allocation_result.component_allocations:
            return

        # 获取主要分配信息（通常是第一个器件的信息）
        primary_allocation = allocation_result.component_allocations[0]

        # 更新基本分配字段
        io_point.allocated_cabinet = primary_allocation.cabinet_name
        io_point.allocated_rail = primary_allocation.rail_name
        io_point.allocation_status = "已分配"

        # 存储完整的多器件分配信息
        io_point.component_allocations = allocation_result.component_allocations

        # 为向后兼容，设置传统字段
        io_point.cabinet = primary_allocation.cabinet_name
        io_point.rack = "R01"  # 简化的机架信息
        io_point.slot = "01"   # 简化的槽位信息
        io_point.channel = "01"  # 简化的通道信息

        # 如果有ETP器件，使用其详细信息
        etp_allocations = allocation_result.get_allocations_by_type(
            ComponentType.ETP if hasattr(allocation_result, 'get_allocations_by_type') else None
        )
        if etp_allocations:
            etp_alloc = etp_allocations[0]
            if etp_alloc.slot:
                io_point.allocated_slot = etp_alloc.slot
            if etp_alloc.channel:
                io_point.allocated_channel = etp_alloc.channel

    def _convert_dict_to_actual_cabinet(self, cabinet_dict: Dict[str, Any]) -> Optional[Any]:
        """
        将机柜字典转换为ActualCabinet对象

        Args:
            cabinet_dict: 机柜字典数据

        Returns:
            ActualCabinet对象，如果转换失败返回None
        """
        try:
            from core.component_allocation_models import ActualCabinet, RailInstance

            # 提取基本信息
            cabinet_name = cabinet_dict.get('name', '')
            template_name = cabinet_dict.get('template', cabinet_dict.get('type', 'Unknown'))
            location = cabinet_dict.get('location', '')
            cabinet_type = cabinet_dict.get('type', 'Marshalling')

            # 添加调试信息
            self.logger.debug(f"转换机柜字典: name='{cabinet_name}', template='{template_name}', type='{cabinet_type}'")

            # 验证机柜名称不是模板名称
            if cabinet_name in ['PPG BAR', 'PPG RIO', 'PPG SYS', 'PPG_BAR', 'PPG_RIO', 'PPG_SYS']:
                self.logger.warning(f"检测到机柜名称是模板名称: {cabinet_name}，这可能导致日志显示问题")
                # 如果有更好的名称字段，可以在这里尝试使用
                actual_name_candidates = [
                    cabinet_dict.get('actual_name'),
                    cabinet_dict.get('pidb_name'),
                    cabinet_dict.get('real_name'),
                    cabinet_dict.get('cabinet_name')
                ]

                for candidate in actual_name_candidates:
                    if candidate and candidate != cabinet_name:
                        self.logger.info(f"使用替代机柜名称: {candidate} (原名称: {cabinet_name})")
                        cabinet_name = candidate
                        break

            # 转换导轨信息
            rails_data = cabinet_dict.get('rails', [])
            rails = []

            for rail_dict in rails_data:
                try:
                    # 提取导轨属性
                    rail_name = rail_dict.get('name', '')
                    position = rail_dict.get('position', '')
                    length = float(rail_dict.get('length', 1000))
                    io_type = rail_dict.get('io_type', 'Mixed')
                    intrinsic = rail_dict.get('intrinsic', 'NIS')
                    voltage_level = int(rail_dict.get('voltage_level', 24))

                    # 提取支持的器件类型
                    supported_part_types = []
                    for i in range(1, 10):  # 检查PartType01到PartType09
                        part_type_key = f'PartType{i:02d}'
                        part_type = rail_dict.get(part_type_key, '')
                        if part_type and part_type.strip():
                            supported_part_types.append(part_type.strip())

                    # 如果没有找到PartType字段，尝试其他可能的字段名
                    if not supported_part_types:
                        # 尝试直接的supported_part_types字段
                        direct_types = rail_dict.get('supported_part_types', [])
                        if isinstance(direct_types, list):
                            supported_part_types = direct_types
                        elif isinstance(direct_types, str):
                            supported_part_types = [direct_types]

                    rail = RailInstance(
                        name=rail_name,
                        position=position,
                        length=length,
                        io_type=io_type,
                        intrinsic=intrinsic,
                        voltage_level=voltage_level,
                        supported_part_types=supported_part_types,
                        reserved_from=float(rail_dict.get('reserved_from', 0)),
                        reserved_to=float(rail_dict.get('reserved_to', 0))
                    )

                    rails.append(rail)

                except Exception as e:
                    self.logger.warning(f"转换导轨失败 {rail_dict.get('name', 'Unknown')}: {e}")
                    continue

            # 创建ActualCabinet对象
            actual_cabinet = ActualCabinet(
                name=cabinet_name,
                template_name=template_name,
                location=location,
                cabinet_type=cabinet_type,
                rails=rails,
                properties=cabinet_dict
            )

            self.logger.debug(f"成功转换机柜 {cabinet_name} 为ActualCabinet对象，包含 {len(rails)} 个导轨")
            return actual_cabinet

        except Exception as e:
            self.logger.error(f"转换机柜字典为ActualCabinet对象失败: {e}")
            return None

    def _get_cabinet_type_from_typical(self, typical_name: str) -> str:
        """
        从典型回路定义中获取机柜类型要求

        Args:
            typical_name: 典型回路名称

        Returns:
            机柜类型字符串
        """
        try:
            # 从分配引擎获取典型回路定义
            if hasattr(self, 'allocation_engine') and self.allocation_engine:
                typical_manager = getattr(self.allocation_engine, 'typical_manager', None)
                if typical_manager:
                    typical_def = typical_manager.get_typical_by_name(typical_name)
                    if typical_def and hasattr(typical_def, 'required_cabinet_type'):
                        cabinet_type = typical_def.required_cabinet_type
                        self.logger.debug(f"从典型回路 {typical_name} 获取机柜类型: {cabinet_type}")
                        return cabinet_type

            # 回退到基于名称的推断
            typical_lower = typical_name.lower()
            if 'remote' in typical_lower:
                return 'System/Marshalling'
            elif any(signal in typical_lower for signal in ['di', 'do']):
                return 'Marshalling'
            elif any(signal in typical_lower for signal in ['ai', 'ao']):
                return 'System'
            else:
                return 'Marshalling'  # 默认

        except Exception as e:
            self.logger.warning(f"获取典型回路 {typical_name} 机柜类型失败: {e}")
            return 'Marshalling'  # 默认回退

    def _allocate_cable_with_polling(self, cable: Dict[str, Any],
                                   cabinets: List[Dict[str, Any]],
                                   wiring_typicals: Dict[str, Any]) -> AllocationResult:
        """
        使用机柜轮询机制分配电缆

        Args:
            cable: 电缆数据
            cabinets: 机柜列表
            wiring_typicals: 典型回路字典

        Returns:
            分配结果
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])

        result = AllocationResult()

        # 获取机柜优先级列表
        cabinet_priority_list = self._get_cabinet_priority_list(cable, cabinets)

        if not cabinet_priority_list:
            error_msg = f"无法为电缆 {cable_name} 找到任何可用机柜"
            result.errors.append(error_msg)
            result.failed_points.extend(io_points)
            return result

        self.logger.info(f"电缆 {cable_name} 机柜轮询顺序: {[cab.get('name', 'Unknown') for cab in cabinet_priority_list]}")

        # 按优先级尝试每个机柜
        for attempt, target_cabinet in enumerate(cabinet_priority_list, 1):
            cabinet_name = target_cabinet.get('name', 'Unknown')
            cabinet_type = target_cabinet.get('type', 'Unknown')

            self.logger.info(f"尝试 {attempt}/{len(cabinet_priority_list)}: 使用机柜 {cabinet_name} ({cabinet_type})")

            # 尝试在当前机柜中分配所有I/O点
            attempt_result = self._try_allocate_in_cabinet(
                cable, target_cabinet, wiring_typicals, cabinets
            )

            if attempt_result.success:
                # 分配成功，返回结果
                self.logger.info(f"电缆 {cable_name} 成功分配到机柜 {cabinet_name}")
                return attempt_result
            else:
                # 分配失败，记录原因并尝试下一个机柜
                self.logger.warning(f"机柜 {cabinet_name} 分配失败: {', '.join(attempt_result.errors)}")

                # 如果不是最后一个机柜，继续尝试
                if attempt < len(cabinet_priority_list):
                    self.logger.info(f"继续尝试下一个机柜...")
                    continue

        # 所有机柜都尝试失败
        error_msg = f"电缆 {cable_name} 在所有 {len(cabinet_priority_list)} 个机柜中都分配失败"
        result.errors.append(error_msg)
        result.failed_points.extend(io_points)

        # 收集所有尝试的错误信息
        for attempt, target_cabinet in enumerate(cabinet_priority_list, 1):
            cabinet_name = target_cabinet.get('name', 'Unknown')
            result.errors.append(f"机柜 {cabinet_name} (尝试 {attempt}): 分配失败")

        self.logger.error(error_msg)
        return result

    def _get_cabinet_priority_list(self, cable: Dict[str, Any],
                                 cabinets: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        获取机柜优先级列表

        Args:
            cable: 电缆数据
            cabinets: 机柜列表

        Returns:
            按优先级排序的机柜列表
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])

        if not io_points:
            return []

        # 分析I/O点的典型回路需求
        required_cabinet_types = set()
        for io_point in io_points:
            typical_name = getattr(io_point, 'wiring_typical', '')
            if typical_name:
                cabinet_type = self._get_cabinet_type_from_typical(typical_name)
                if cabinet_type:
                    required_cabinet_types.add(cabinet_type)

        if not required_cabinet_types:
            required_cabinet_types.add('Marshalling')  # 默认需求

        # 按机柜类型分组
        cabinet_groups = {
            'Marshalling': [],
            'System/Marshalling': [],
            'System': []
        }

        for cabinet in cabinets:
            cabinet_type = cabinet.get('type', '')
            rails = cabinet.get('rails', [])

            # 只考虑有导轨的机柜
            if len(rails) > 0 and cabinet_type in cabinet_groups:
                cabinet_groups[cabinet_type].append(cabinet)

        # 构建优先级列表
        priority_list = []

        # 根据需求类型确定优先级顺序
        for required_type in required_cabinet_types:
            if required_type == 'Marshalling':
                # Marshalling需求：优先Marshalling，然后System/Marshalling
                priority_list.extend(cabinet_groups['Marshalling'])
                priority_list.extend(cabinet_groups['System/Marshalling'])
            elif required_type == 'System':
                # System需求：优先System，然后System/Marshalling
                priority_list.extend(cabinet_groups['System'])
                priority_list.extend(cabinet_groups['System/Marshalling'])
            elif required_type == 'System/Marshalling':
                # 混合需求：优先System/Marshalling，然后其他
                priority_list.extend(cabinet_groups['System/Marshalling'])
                priority_list.extend(cabinet_groups['Marshalling'])
                priority_list.extend(cabinet_groups['System'])

        # 去重，保持顺序
        seen = set()
        unique_priority_list = []
        for cabinet in priority_list:
            cabinet_name = cabinet.get('name', '')
            if cabinet_name not in seen:
                seen.add(cabinet_name)
                unique_priority_list.append(cabinet)

        self.logger.debug(f"电缆 {cable_name} 机柜优先级列表: {[cab.get('name', 'Unknown') for cab in unique_priority_list]}")
        return unique_priority_list

    def _try_allocate_in_cabinet(self, cable: Dict[str, Any],
                               target_cabinet: Dict[str, Any],
                               wiring_typicals: Dict[str, Any],
                               all_cabinets: List[Dict[str, Any]]) -> AllocationResult:
        """
        尝试在指定机柜中分配电缆的所有I/O点

        Args:
            cable: 电缆数据
            target_cabinet: 目标机柜
            wiring_typicals: 典型回路字典
            all_cabinets: 所有可用机柜列表

        Returns:
            分配结果
        """
        cable_name = cable.get('name', 'Unknown')
        io_points = cable.get('io_points', [])
        cabinet_name = target_cabinet.get('name', 'Unknown')

        result = AllocationResult()

        # 为每个I/O点分配具体位置
        for io_point in io_points:
            allocation_success = self._allocate_single_point(
                io_point, target_cabinet, wiring_typicals, all_cabinets
            )

            if allocation_success:
                result.allocated_points.append(io_point)
                self.logger.debug(f"成功分配I/O点: {io_point.tag} 到机柜 {cabinet_name}")
            else:
                result.failed_points.append(io_point)
                error_msg = f"I/O点 {io_point.tag} 在机柜 {cabinet_name} 中分配失败"
                result.errors.append(error_msg)
                self.logger.debug(error_msg)

        # 判断整体分配是否成功
        result.success = len(result.failed_points) == 0

        if result.success:
            self.logger.info(f"电缆 {cable_name} 在机柜 {cabinet_name} 中完全分配成功")
        else:
            self.logger.warning(f"电缆 {cable_name} 在机柜 {cabinet_name} 中部分分配失败: "
                              f"{len(result.failed_points)}/{len(io_points)} 个I/O点失败")

        return result
