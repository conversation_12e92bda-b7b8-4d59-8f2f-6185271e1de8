#!/usr/bin/env python3
"""
测试模块导入
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_imports():
    """测试各个模块的导入"""
    try:
        print("测试核心模块导入...")
        from core.data_models import IOPoint, Cable, Rail, Rack, Cabinet
        print("✓ core.data_models")
        
        from core.data_loader import DataLoader
        print("✓ core.data_loader")
        
        from core.validator import IODBValidator
        print("✓ core.validator")
        
        from core.allocator import IOAllocator
        print("✓ core.allocator")
        
        from core.logger import setup_logger, get_logger
        print("✓ core.logger")
        
        print("\n测试工具模块导入...")
        from utils.config_manager import ConfigManager
        print("✓ utils.config_manager")
        
        from utils.excel_utils import ExcelReader, ExcelWriter
        print("✓ utils.excel_utils")
        
        from utils.xml_utils import XMLProcessor
        print("✓ utils.xml_utils")
        
        print("\n测试GUI模块导入...")
        from gui.main_window import MainWindow
        print("✓ gui.main_window")
        
        from gui.allocation_widget import AllocationWidget
        print("✓ gui.allocation_widget")
        
        from gui.config_widget import ConfigWidget
        print("✓ gui.config_widget")
        
        from gui.xml_editor_widget import XMLEditorWidget
        print("✓ gui.xml_editor_widget")
        
        print("\n所有模块导入成功！")
        return True
        
    except ImportError as e:
        print(f"✗ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"✗ 其他错误: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    try:
        print("\n测试基本功能...")
        
        # 测试配置管理器
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.load_config()
        print("✓ 配置管理器工作正常")
        
        # 测试日志系统
        from core.logger import setup_logger, get_logger
        setup_logger(config.get('logging', {}))
        logger = get_logger(__name__)
        logger.info("测试日志消息")
        print("✓ 日志系统工作正常")
        
        # 测试数据模型
        from core.data_models import IOPoint, SignalType
        io_point = IOPoint(
            tag="TEST_TAG",
            cable_name="TEST_CABLE",
            pair_number=1,
            signal_type=SignalType.AI,
            is_intrinsic=True,
            system="TEST_SYSTEM",
            location="TEST_LOCATION",
            cable_type="TEST_TYPE",
            wiring_typical="TEST_TYPICAL"
        )
        print("✓ 数据模型工作正常")
        
        print("\n基本功能测试通过！")
        return True
        
    except Exception as e:
        print(f"✗ 功能测试失败: {e}")
        return False

if __name__ == "__main__":
    print("EWReborn 模块导入测试")
    print("=" * 50)
    
    import_success = test_imports()
    if import_success:
        functionality_success = test_basic_functionality()
        
        if functionality_success:
            print("\n" + "=" * 50)
            print("所有测试通过！可以启动主程序。")
        else:
            print("\n" + "=" * 50)
            print("功能测试失败，请检查代码。")
    else:
        print("\n" + "=" * 50)
        print("模块导入失败，请检查依赖项。")
