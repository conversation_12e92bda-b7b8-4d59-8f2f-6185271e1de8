"""
数据加载器 - 简化版本
负责加载机柜配置、典型回路、IODB和PIDB数据
"""

import json
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
import pandas as pd

from core.logger import get_logger
from core.data_models import IOPoint, SignalType
from core.cabinet_template_parser import CabinetTemplateParser
from utils.excel_utils_simple import ExcelReader
from utils.xml_parser import XMLParser


class DataLoader:
    """数据加载器类"""
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化数据加载器

        Args:
            config: 配置字典
        """
        self.config = config
        self.logger = get_logger(__name__)
        self.excel_reader = ExcelReader()
        self.xml_parser = XMLParser()
        self.cabinet_template_parser = CabinetTemplateParser()

        # 获取数据路径配置
        self.data_paths = config.get('data_paths', {})
        self.cabinet_profiles_path = self.data_paths.get('cabinet_profiles', '01B_Cabinet Templates')
        self.wiring_typical_path = self.data_paths.get('wiring_typical', '01C_Wiring Typical')
        self.iodb_path = self.data_paths.get('iodb', '04A_IODB')
        self.pidb_path = self.data_paths.get('pidb', '04B_PIDB')
    
    def load_all_data(self) -> Dict[str, Any]:
        """
        加载所有配置数据
        
        Returns:
            包含所有数据的字典
        """
        self.logger.info("开始加载所有数据")
        
        result = {
            'cabinet_profiles': {},
            'wiring_typicals': {},
            'load_status': {
                'cabinet_profiles': False,
                'wiring_typicals': False,
                'iodb': False,
                'pidb': False
            }
        }
        
        try:
            # 加载机柜配置
            cabinet_profiles = self.load_cabinet_profiles()
            if cabinet_profiles:
                result['cabinet_profiles'] = cabinet_profiles
                result['load_status']['cabinet_profiles'] = True
            
            # 加载典型回路
            wiring_typicals = self.load_wiring_typicals()
            if wiring_typicals:
                result['wiring_typicals'] = wiring_typicals
                result['load_status']['wiring_typicals'] = True
            
            self.logger.info("数据加载完成")
            return result
            
        except Exception as e:
            self.logger.error(f"数据加载失败: {e}")
            raise
    
    def load_cabinet_profiles(self) -> Dict[str, Any]:
        """
        加载机柜配置文件
        
        Returns:
            机柜配置字典
        """
        self.logger.info(f"加载机柜配置: {self.cabinet_profiles_path}")
        
        cabinet_profiles = {}
        cabinet_path = Path(self.cabinet_profiles_path)
        
        if not cabinet_path.exists():
            self.logger.warning(f"机柜配置目录不存在: {cabinet_path}")
            return cabinet_profiles
        
        # 查找XML配置文件
        xml_files = list(cabinet_path.glob("*.xml"))
        # 也查找子目录中的XML文件
        for subdir in cabinet_path.iterdir():
            if subdir.is_dir():
                xml_files.extend(subdir.glob("*.xml"))

        self.logger.info(f"找到{len(xml_files)}个机柜配置文件")

        for xml_file in xml_files:
            try:
                config_data = self.xml_parser.parse_cabinet_xml(str(xml_file))
                cabinet_name = xml_file.stem
                cabinet_profiles[cabinet_name] = config_data
                self.logger.debug(f"加载机柜配置: {cabinet_name}")
            except Exception as e:
                self.logger.error(f"加载机柜配置文件失败 {xml_file}: {e}")
        
        self.logger.info("机柜配置文件加载成功")
        return cabinet_profiles
    
    def load_wiring_typicals(self) -> Dict[str, Any]:
        """
        加载典型回路文件
        
        Returns:
            典型回路字典
        """
        self.logger.info(f"加载典型回路: {self.wiring_typical_path}")
        
        wiring_typicals = {}
        typical_path = Path(self.wiring_typical_path)
        
        if not typical_path.exists():
            self.logger.warning(f"典型回路目录不存在: {typical_path}")
            return wiring_typicals
        
        # 查找XML文件
        xml_files = list(typical_path.glob("*.xml"))
        self.logger.info(f"找到{len(xml_files)}个典型回路文件")
        
        for xml_file in xml_files:
            try:
                # 使用XML解析器解析典型回路文件
                typical_config = self.xml_parser.parse_wiring_typical_xml(str(xml_file))
                typical_name = xml_file.stem
                wiring_typicals[typical_name] = typical_config
                self.logger.debug(f"加载典型回路: {typical_name}")
            except Exception as e:
                self.logger.error(f"加载典型回路文件失败 {xml_file}: {e}")
        
        self.logger.info("典型回路文件加载成功")
        return wiring_typicals
    
    def load_iodb_data(self, file_path: str) -> Dict[str, Any]:
        """
        加载IODB数据
        
        Args:
            file_path: IODB文件路径
            
        Returns:
            IODB数据字典
        """
        self.logger.info(f"加载IODB数据: {file_path}")
        
        try:
            # 使用Excel读取器读取文件
            excel_data = self.excel_reader.read_excel_file(file_path)
            
            # 处理Excel数据
            io_points = []
            cables = {}
            
            # 假设第一个工作表包含IODB数据
            if isinstance(excel_data, dict):
                sheet_name = list(excel_data.keys())[0]
                df = excel_data[sheet_name]
            else:
                df = excel_data
            
            self.logger.info(f"IODB工作表包含 {len(df)} 行数据")
            
            # 处理每一行数据
            for index, row in df.iterrows():
                try:
                    # 创建IOPoint对象，使用实际的列名
                    io_point = IOPoint(
                        tag=str(row.get('tagname', row.get('Tag', ''))),
                        signal_type=self._parse_signal_type(str(row.get('signaltype', row.get('Signal_Type', 'DI')))),
                        description=str(row.get('description', row.get('Description', ''))),
                        location=str(row.get('location', row.get('Location', ''))),
                        cabinet=str(row.get('Cabinet', '')),
                        rack=str(row.get('Rack', '')),
                        slot=str(row.get('Slot', '')),
                        channel=str(row.get('Channel', '')),
                        cable_name=str(row.get('cable name', row.get('Cable_Name', ''))),
                        pair_number=int(row.get('pair number', row.get('Pair_Number', 1))),
                        is_intrinsic=str(row.get('is', '')).upper() == 'IS',
                        system=str(row.get('system', row.get('System', ''))),
                        cable_type=str(row.get('cable type', row.get('Cable_Type', ''))),
                        wiring_typical=str(row.get('wiring typical', row.get('Wiring_Typical', '')))
                    )
                    io_points.append(io_point)

                    # 处理电缆信息
                    cable_name = str(row.get('cable name', row.get('Cable_Name', '')))
                    if cable_name and cable_name != 'nan' and cable_name.strip():
                        if cable_name not in cables:
                            cables[cable_name] = {
                                'name': cable_name,
                                'io_points': [],
                                'pair_size': int(row.get('cable size', row.get('Pair_Size', 1)))
                            }

                        cables[cable_name]['io_points'].append(io_point)
                        
                except Exception as e:
                    self.logger.warning(f"处理IODB行数据失败 (行{index}): {e}")
                    continue
            
            self.logger.info(f"成功处理 {len(io_points)} 个I/O点，{len(cables)} 条电缆")
            self.logger.info(f"成功加载IODB数据: {len(io_points)}个I/O点，{len(cables)}条电缆")
            
            return {
                'io_points': io_points,
                'cables': cables,
                'summary': {
                    'total_points': len(io_points),
                    'total_cables': len(cables)
                }
            }
            
        except Exception as e:
            self.logger.error(f"加载IODB数据失败: {e}")
            raise
    
    def load_pidb_data(self, file_path: str) -> Dict[str, Any]:
        """
        加载PIDB数据

        Args:
            file_path: PIDB文件路径

        Returns:
            PIDB数据字典
        """
        self.logger.info(f"加载PIDB数据: {file_path}")

        try:
            # 使用Excel读取器读取文件
            excel_data = self.excel_reader.read_excel_file(file_path)

            # 解析PIDB数据结构
            racks = []
            cabinets = []

            # 分析Excel数据结构
            if isinstance(excel_data, dict):
                for sheet_name, df in excel_data.items():
                    self.logger.info(f"分析PIDB工作表: {sheet_name}, 行数: {len(df)}")

                    # 尝试从工作表名称推断内容类型
                    if 'rack' in sheet_name.lower() or 'chassis' in sheet_name.lower():
                        sheet_racks = self._extract_racks_from_pidb(df, sheet_name)
                        racks.extend(sheet_racks)
                    elif 'cabinet' in sheet_name.lower():
                        sheet_cabinets = self._extract_cabinets_from_pidb(df, sheet_name)
                        cabinets.extend(sheet_cabinets)
                    else:
                        # 通用解析，尝试识别机架和机柜信息
                        sheet_racks, sheet_cabinets = self._parse_generic_pidb_sheet(df, sheet_name)
                        racks.extend(sheet_racks)
                        cabinets.extend(sheet_cabinets)

            # 如果没有找到明确的机架和机柜信息，创建默认结构
            if not racks and not cabinets:
                self.logger.info("未找到明确的机架和机柜信息，创建默认结构")
                # 基于项目信息创建默认机柜（使用实际机柜名称而不是模板名称）
                cabinets = [
                    {'name': 'E-1001', 'template': 'PPG BAR', 'type': 'Marshalling', 'location': 'Field Area'},
                    {'name': 'E-1002', 'template': 'PPG RIO', 'type': 'System/Marshalling', 'location': 'Field Area'},
                    {'name': 'E-2001', 'template': 'PPG SYS', 'type': 'System', 'location': 'Control Room'}
                ]

                # 为每个机柜创建默认机架
                for cabinet in cabinets:
                    for i in range(2):  # 每个机柜2个机架
                        racks.append({
                            'name': f"{cabinet['name']}_R{i+1:02d}",
                            'cabinet': cabinet['name'],
                            'max_slots': 16,
                            'type': 'Standard Rack'
                        })

            return {
                'racks': racks,
                'cabinets': cabinets,
                'raw_data': excel_data,
                'summary': {
                    'total_racks': len(racks),
                    'total_cabinets': len(cabinets),
                    'loaded': True
                }
            }

        except Exception as e:
            self.logger.error(f"加载PIDB数据失败: {e}")
            raise
    
    def create_data_models(self, iodb_data: Dict[str, Any], pidb_data: Dict[str, Any], 
                          cabinet_profiles: Dict[str, Any], wiring_typicals: Dict[str, Any]) -> Dict[str, Any]:
        """
        创建数据模型对象
        
        Args:
            iodb_data: IODB数据
            pidb_data: PIDB数据
            cabinet_profiles: 机柜配置
            wiring_typicals: 典型回路
            
        Returns:
            数据模型字典
        """
        self.logger.info("创建数据模型对象")
        
        try:
            # 从IODB数据提取信息
            io_points = iodb_data.get('io_points', [])
            cables_dict = iodb_data.get('cables', {})
            cables = list(cables_dict.values())
            
            # 创建机柜对象 - 优先使用PIDB数据，回退到配置文件
            cabinets = []

            # 首先尝试使用PIDB数据中的机柜
            pidb_cabinets = pidb_data.get('cabinets', [])
            if pidb_cabinets:
                self.logger.info(f"使用PIDB数据中的 {len(pidb_cabinets)} 个机柜")
                for pidb_cabinet in pidb_cabinets:
                    # 获取机柜模板配置 - 使用真实的模板解析器
                    template_name = pidb_cabinet.get('template', '')

                    # 优先使用传入的cabinet_profiles，回退到模板解析器
                    if template_name in cabinet_profiles:
                        cabinet_config = cabinet_profiles[template_name]
                        self.logger.debug(f"使用传入的机柜配置: {template_name}")
                    else:
                        # 使用模板解析器从XML文件加载
                        cabinet_config = self.cabinet_template_parser.parse_cabinet_template(template_name)
                        if cabinet_config:
                            self.logger.debug(f"从XML模板解析机柜配置: {template_name}")
                        else:
                            self.logger.warning(f"未找到机柜模板: {template_name}，使用默认配置")
                            cabinet_config = {'type': 'Marshalling', 'rails': []}

                    cabinet_obj = {
                        'name': pidb_cabinet['name'],  # 使用PIDB中的真实机柜名称
                        'template': template_name,     # 保留模板名称
                        'type': pidb_cabinet.get('type', cabinet_config.get('type', 'Marshalling')),
                        'location': pidb_cabinet.get('location', ''),
                        'config': cabinet_config,
                        'rails': cabinet_config.get('rails', []),
                        'source': 'PIDB'  # 标记数据来源
                    }
                    cabinets.append(cabinet_obj)
                    self.logger.debug(f"PIDB机柜 {pidb_cabinet['name']} (模板: {template_name}) 创建成功，包含 {len(cabinet_obj['rails'])} 个导轨")
            else:
                # 回退到使用机柜配置文件或模板解析器
                if cabinet_profiles:
                    self.logger.info(f"PIDB中无机柜数据，使用配置文件中的 {len(cabinet_profiles)} 个机柜模板")
                    for cabinet_name, cabinet_config in cabinet_profiles.items():
                        cabinet_obj = {
                            'name': cabinet_name,  # 这里使用的是模板名称
                            'template': cabinet_name,
                            'type': cabinet_config.get('type', 'Marshalling'),
                            'location': '',
                            'config': cabinet_config,
                            'rails': cabinet_config.get('rails', []),
                            'source': 'Config'  # 标记数据来源
                        }
                        cabinets.append(cabinet_obj)
                        self.logger.debug(f"配置机柜 {cabinet_name} 创建成功，包含 {len(cabinet_obj['rails'])} 个导轨")
                else:
                    # 使用模板解析器加载所有可用模板
                    self.logger.info("PIDB和配置文件中都无机柜数据，使用模板解析器加载所有可用模板")
                    all_templates = self.cabinet_template_parser.load_all_cabinet_templates()
                    for template_name, cabinet_config in all_templates.items():
                        cabinet_obj = {
                            'name': template_name,  # 使用模板名称作为机柜名称
                            'template': template_name,
                            'type': cabinet_config.get('type', 'Marshalling'),
                            'location': '',
                            'config': cabinet_config,
                            'rails': cabinet_config.get('rails', []),
                            'source': 'Template'  # 标记数据来源
                        }
                        cabinets.append(cabinet_obj)
                        self.logger.debug(f"模板机柜 {template_name} 创建成功，包含 {len(cabinet_obj['rails'])} 个导轨")
            
            # 创建典型回路对象
            wiring_typical_objects = []
            for typical_name, typical_config in wiring_typicals.items():
                # 创建一个简单的对象类来模拟典型回路
                class WiringTypical:
                    def __init__(self, name, config):
                        self.name = name
                        self.config = config
                        self.components = config.get('components', [])

                typical_obj = WiringTypical(typical_name, typical_config)
                wiring_typical_objects.append(typical_obj)
            
            self.logger.info(f"数据模型创建完成: {len(io_points)}个I/O点, {len(cables)}条电缆, {len(cabinets)}个机柜, {len(wiring_typical_objects)}个典型回路")
            
            return {
                'io_points': io_points,
                'cables': cables,
                'cabinets': cabinets,
                'wiring_typicals': wiring_typical_objects
            }
            
        except Exception as e:
            self.logger.error(f"创建数据模型失败: {e}")
            raise
    
    def _extract_racks_from_pidb(self, df: pd.DataFrame, sheet_name: str) -> List[Dict[str, Any]]:
        """
        从PIDB工作表提取机架信息

        Args:
            df: 数据框
            sheet_name: 工作表名称

        Returns:
            机架列表
        """
        racks = []
        # 实现机架提取逻辑
        # 这里是简化实现，实际需要根据PIDB格式调整
        return racks

    def _extract_cabinets_from_pidb(self, df: pd.DataFrame, sheet_name: str) -> List[Dict[str, Any]]:
        """
        从PIDB工作表提取机柜信息

        Args:
            df: 数据框
            sheet_name: 工作表名称

        Returns:
            机柜列表
        """
        cabinets = []

        try:
            self.logger.info(f"开始从PIDB工作表 '{sheet_name}' 提取机柜信息，共 {len(df)} 行数据")

            # 分析列名，寻找机柜相关的列
            columns = df.columns.tolist()
            self.logger.debug(f"PIDB工作表列名: {columns}")

            # 建立列名映射（支持不同的列名格式）
            column_mapping = self._build_cabinet_column_mapping(columns)

            if not column_mapping.get('cabinet_name'):
                self.logger.warning(f"在工作表 '{sheet_name}' 中未找到机柜名称列")
                return cabinets

            # 逐行处理机柜数据
            for index, row in df.iterrows():
                try:
                    cabinet_data = self._extract_cabinet_from_row(row, column_mapping, index)
                    if cabinet_data:
                        cabinets.append(cabinet_data)
                        self.logger.debug(f"提取机柜: {cabinet_data['name']} (模板: {cabinet_data.get('template', 'Unknown')})")

                except Exception as e:
                    self.logger.warning(f"处理第 {index+2} 行机柜数据失败: {e}")
                    continue

            self.logger.info(f"成功从PIDB提取 {len(cabinets)} 个机柜")

            # 打印提取的机柜信息用于调试
            if cabinets:
                self.logger.info("提取的机柜列表:")
                for cabinet in cabinets:
                    self.logger.info(f"  - {cabinet['name']} (模板: {cabinet.get('template', 'Unknown')}, 类型: {cabinet.get('type', 'Unknown')})")

            return cabinets

        except Exception as e:
            self.logger.error(f"从PIDB工作表提取机柜信息失败: {e}")
            return cabinets

    def _build_cabinet_column_mapping(self, columns: List[str]) -> Dict[str, str]:
        """
        建立机柜列名映射

        Args:
            columns: 列名列表

        Returns:
            列名映射字典
        """
        mapping = {}

        # 机柜名称列的可能名称
        cabinet_name_candidates = ['Cabinet', 'cabinet', 'CabinetName', 'cabinet_name', 'Name', 'name']
        # 机柜模板列的可能名称
        template_candidates = ['CabinetTemplate', 'cabinet_template', 'Template', 'template', 'Type', 'type']
        # 机柜编号列的可能名称
        cabinet_no_candidates = ['CabinetNo', 'cabinet_no', 'No', 'no', 'Number', 'number']
        # 位置列的可能名称
        location_candidates = ['Location', 'location', 'Area', 'area', 'Zone', 'zone']

        # 查找匹配的列名
        for col in columns:
            if col in cabinet_name_candidates:
                mapping['cabinet_name'] = col
            elif col in template_candidates:
                mapping['template'] = col
            elif col in cabinet_no_candidates:
                mapping['cabinet_no'] = col
            elif col in location_candidates:
                mapping['location'] = col

        self.logger.debug(f"机柜列名映射: {mapping}")
        return mapping

    def _extract_cabinet_from_row(self, row, column_mapping: Dict[str, str], index: int) -> Dict[str, Any]:
        """
        从数据行提取机柜信息

        Args:
            row: 数据行
            column_mapping: 列名映射
            index: 行索引

        Returns:
            机柜数据字典
        """
        try:
            # 获取机柜名称
            cabinet_name_col = column_mapping.get('cabinet_name')
            if not cabinet_name_col:
                return None

            cabinet_name = str(row[cabinet_name_col]).strip()
            if not cabinet_name or cabinet_name.lower() in ['nan', 'none', '']:
                return None

            # 获取模板名称
            template_col = column_mapping.get('template')
            template_name = str(row[template_col]).strip() if template_col else 'Unknown'
            if template_name.lower() in ['nan', 'none', '']:
                template_name = 'Unknown'

            # 获取机柜编号
            cabinet_no_col = column_mapping.get('cabinet_no')
            cabinet_no = str(row[cabinet_no_col]).strip() if cabinet_no_col else ''
            if cabinet_no.lower() in ['nan', 'none', '']:
                cabinet_no = ''

            # 获取位置信息
            location_col = column_mapping.get('location')
            location = str(row[location_col]).strip() if location_col else ''
            if location.lower() in ['nan', 'none', '']:
                location = ''

            # 根据模板名称推断机柜类型
            cabinet_type = self._infer_cabinet_type_from_template(template_name)

            cabinet_data = {
                'name': cabinet_name,  # 使用PIDB中的真实机柜名称
                'template': template_name,  # 模板名称
                'type': cabinet_type,
                'location': location,
                'cabinet_no': cabinet_no,
                'source': 'PIDB',
                'properties': row.to_dict()  # 保存原始行数据
            }

            return cabinet_data

        except Exception as e:
            self.logger.error(f"提取第 {index+2} 行机柜数据失败: {e}")
            return None

    def _infer_cabinet_type_from_template(self, template_name: str) -> str:
        """
        根据模板名称推断机柜类型

        Args:
            template_name: 模板名称

        Returns:
            机柜类型
        """
        template_lower = template_name.lower()

        if 'sys' in template_lower:
            return 'System'
        elif 'bar' in template_lower or 'rel' in template_lower:
            return 'Marshalling'
        elif 'rio' in template_lower:
            return 'System/Marshalling'
        else:
            return 'Marshalling'  # 默认类型

    def _parse_generic_pidb_sheet(self, df: pd.DataFrame, sheet_name: str) -> Tuple[List[Dict[str, Any]], List[Dict[str, Any]]]:
        """
        通用PIDB工作表解析

        Args:
            df: 数据框
            sheet_name: 工作表名称

        Returns:
            (机架列表, 机柜列表)
        """
        racks = []
        cabinets = []
        # 实现通用解析逻辑
        return racks, cabinets

    def _parse_signal_type(self, signal_type_str: str) -> SignalType:
        """
        解析信号类型字符串

        Args:
            signal_type_str: 信号类型字符串

        Returns:
            SignalType枚举值
        """
        try:
            return SignalType(signal_type_str.upper())
        except ValueError:
            self.logger.warning(f"未知信号类型: {signal_type_str}，使用默认值DI")
            return SignalType.DI
