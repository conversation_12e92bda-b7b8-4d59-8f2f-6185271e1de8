#!/usr/bin/env python3
"""
测试智能机柜选择功能
验证机柜轮询机制、按机柜类型匹配、混合类型机柜处理等功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger import get_logger
from core.component_allocation_engine import ComponentAllocationEngine
from core.component_allocation_models import ActualCabinet, RailInstance, ComponentDefinition, ComponentType
from core.wiring_typical_parser import WiringTypicalParser
from core.data_models import IOPoint, SignalType

def create_test_cabinets():
    """创建测试机柜列表"""
    cabinets = []
    
    # 1. 系统柜（只支持系统器件）
    system_cabinet = ActualCabinet(
        name="SYS-001",
        template_name="PPG SYS",
        location="Control Room",
        cabinet_type="System",
        rails=[RailInstance(
            name="Rail_SYS",
            position="System",
            length=2000.0,
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["ETP", "IOModule", "Chassis"]
        )]
    )
    cabinets.append(system_cabinet)
    
    # 2. 辅助柜（只支持辅助器件）
    marshalling_cabinet = ActualCabinet(
        name="MAR-001",
        template_name="PPG BAR",
        location="Field Area",
        cabinet_type="Marshalling",
        rails=[RailInstance(
            name="Rail_MAR",
            position="Marshalling",
            length=2000.0,
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "Barrier"]
        )]
    )
    cabinets.append(marshalling_cabinet)
    
    # 3. 混合类型机柜（支持所有器件）
    mixed_cabinet = ActualCabinet(
        name="MIX-001",
        template_name="PPG RIO",
        location="Mixed Area",
        cabinet_type="System/Marshalling",
        rails=[RailInstance(
            name="Rail_MIX",
            position="Mixed",
            length=3000.0,
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "Barrier", "IOModule", "Chassis"]
        )]
    )
    cabinets.append(mixed_cabinet)
    
    # 4. 空间不足的辅助柜
    small_marshalling_cabinet = ActualCabinet(
        name="MAR-002",
        template_name="PPG BAR Small",
        location="Field Area",
        cabinet_type="Marshalling",
        rails=[RailInstance(
            name="Rail_MAR_Small",
            position="Marshalling",
            length=50.0,  # 很小的空间
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "Barrier"]
        )]
    )
    cabinets.append(small_marshalling_cabinet)
    
    return cabinets

def test_smart_cabinet_selection():
    """测试智能机柜选择功能"""
    print("=== 测试智能机柜选择功能 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        
        # 创建测试机柜
        available_cabinets = create_test_cabinets()
        
        # 解析真实的典型回路
        parser = WiringTypicalParser()
        typical_file = Path("01C_Wiring Typical/DI NIS N.xml")
        
        if not typical_file.exists():
            print(f"❌ 典型回路文件不存在: {typical_file}")
            return False
        
        typical_def = parser.parse_wiring_typical_file(str(typical_file))
        
        # 创建测试I/O点
        io_point = IOPoint(
            tag="TEST-001",
            signal_type=SignalType.DI,
            description="Test Smart Selection",
            wiring_typical="DI NIS N"
        )
        
        print(f"测试I/O点: {io_point.tag}")
        print(f"典型回路: {typical_def.name}")
        print(f"器件数量: {len(typical_def.marshalling_components)}")
        print(f"可用机柜数量: {len(available_cabinets)}")
        
        # 测试场景1：使用不兼容的首选机柜（系统柜），应该自动选择辅助柜
        print(f"\n--- 场景1：首选机柜不兼容，应该自动选择合适机柜 ---")
        system_cabinet = available_cabinets[0]  # 系统柜
        
        result = engine.allocate_io_point_components(
            io_point, system_cabinet, {}, available_cabinets
        )
        
        if result.success:
            print(f"✅ 智能选择成功")
            print(f"   分配的器件数量: {len(result.component_allocations)}")
            if result.component_allocations:
                selected_cabinet_name = result.component_allocations[0].cabinet_name
                print(f"   选择的机柜: {selected_cabinet_name}")
                
                # 验证是否选择了正确的机柜类型
                if selected_cabinet_name in ["MAR-001", "MIX-001"]:
                    print(f"   ✅ 正确选择了兼容的机柜")
                else:
                    print(f"   ❌ 选择的机柜可能不正确")
                    return False
        else:
            print(f"❌ 智能选择失败")
            print(f"   错误: {result.errors}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 智能机柜选择测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cabinet_type_grouping():
    """测试机柜类型分组功能"""
    print("\n=== 测试机柜类型分组功能 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        available_cabinets = create_test_cabinets()
        
        # 测试机柜类型分组
        cabinets_by_type = engine._get_cabinets_by_type(available_cabinets)
        
        print(f"机柜类型分组结果:")
        for cabinet_type, cabinets in cabinets_by_type.items():
            cabinet_names = [c.name for c in cabinets]
            print(f"  {cabinet_type}: {cabinet_names}")
        
        # 验证分组结果
        expected_groups = {
            'System': ['SYS-001'],
            'Marshalling': ['MAR-001', 'MAR-002'],
            'System/Marshalling': ['MIX-001']
        }
        
        for expected_type, expected_names in expected_groups.items():
            if expected_type in cabinets_by_type:
                actual_names = [c.name for c in cabinets_by_type[expected_type]]
                if set(actual_names) == set(expected_names):
                    print(f"  ✅ {expected_type} 类型分组正确")
                else:
                    print(f"  ❌ {expected_type} 类型分组错误: 期望 {expected_names}, 实际 {actual_names}")
                    return False
            else:
                print(f"  ❌ 缺少 {expected_type} 类型分组")
                return False
        
        return True
        
    except Exception as e:
        print(f"❌ 机柜类型分组测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cabinet_accommodation_check():
    """测试机柜容纳能力检查"""
    print("\n=== 测试机柜容纳能力检查 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        available_cabinets = create_test_cabinets()
        
        # 创建测试器件
        test_components = [
            ComponentDefinition(
                name="FTB1",
                component_type=ComponentType.FIELD_TERM_IN,
                part_number="FTB-1",
                cabinet_location="MarshallingCabinet",
                width=12.5
            ),
            ComponentDefinition(
                name="3000510-380C1R",
                component_type=ComponentType.ETP,
                part_number="3000510-380C1R",
                cabinet_location="MarshallingCabinet",
                width=25.0
            )
        ]
        
        io_point = IOPoint(tag="TEST-002", signal_type=SignalType.DI)
        
        print(f"测试器件: {[c.name for c in test_components]}")
        
        # 测试每个机柜的容纳能力
        for cabinet in available_cabinets:
            can_accommodate = engine._check_cabinet_can_accommodate_all_components(
                test_components, cabinet, io_point
            )
            print(f"  机柜 {cabinet.name} ({cabinet.cabinet_type}): {'✅ 可容纳' if can_accommodate else '❌ 不可容纳'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 机柜容纳能力检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试智能机柜选择功能...")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_cabinet_type_grouping())
    test_results.append(test_cabinet_accommodation_check())
    test_results.append(test_smart_cabinet_selection())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！智能机柜选择功能实现成功。")
        print("\n功能特性总结：")
        print("✅ 1. 机柜轮询机制：当器件无法分配到当前机柜时，自动搜索其他可用机柜")
        print("✅ 2. 按机柜类型匹配：优先选择与器件所需机柜类型匹配的机柜")
        print("✅ 3. 扩展兼容性检查：支持混合类型机柜的兼容性验证")
        print("✅ 4. 智能日志记录：记录机柜选择过程和决策原因")
        print("✅ 5. 空间验证：确保选择的机柜有足够空间容纳所有器件")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
