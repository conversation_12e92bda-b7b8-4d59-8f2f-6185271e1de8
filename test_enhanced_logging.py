"""
增强日志记录测试脚本
验证I/O点分配过程中的详细日志记录和可追踪性
"""

import logging
from core.data_models import IOPoint, SignalType
from core.allocator import IOAllocator

def setup_detailed_logging():
    """设置详细的日志记录"""
    # 创建自定义格式器
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # 设置控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)
    console_handler.setFormatter(formatter)
    
    # 设置文件处理器
    file_handler = logging.FileHandler('enhanced_logging_test.log', mode='w', encoding='utf-8')
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(formatter)
    
    # 配置根日志记录器
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.DEBUG)
    root_logger.handlers.clear()  # 清除现有处理器
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    # 确保核心模块的日志级别
    logging.getLogger('core.component_allocation_engine').setLevel(logging.DEBUG)
    logging.getLogger('core.allocator').setLevel(logging.DEBUG)
    logging.getLogger('core.wiring_typical_parser').setLevel(logging.INFO)  # 减少典型回路解析的噪音

def create_test_io_points():
    """创建测试I/O点"""
    test_points = [
        IOPoint(
            tag="AI-1001-TT-001",
            signal_type=SignalType.AI,
            description="Temperature Transmitter 001",
            location="Area 1 - Tank 1001",
            cable_name="CABLE-AI-001",
            pair_number=1,
            is_intrinsic=True,
            system="Temperature Control",
            cable_type="Instrumentation",
            wiring_typical="AI IS BABP"
        ),
        IOPoint(
            tag="DI-2001-LS-001",
            signal_type=SignalType.DI,
            description="Level Switch 001",
            location="Area 2 - Tank 2001",
            cable_name="CABLE-DI-001",
            pair_number=1,
            is_intrinsic=False,
            system="Level Control",
            cable_type="Control",
            wiring_typical="DI NIS REMOTE"
        ),
        IOPoint(
            tag="DO-3001-XV-001",
            signal_type=SignalType.DO,
            description="Control Valve 001",
            location="Area 3 - Line 3001",
            cable_name="CABLE-DO-001",
            pair_number=1,
            is_intrinsic=False,
            system="Flow Control",
            cable_type="Control",
            wiring_typical="DO NIS WET REMOTE"
        )
    ]
    
    return test_points

def create_test_cabinets():
    """创建测试机柜"""
    cabinets = [
        {
            'name': 'E-1001',
            'template': 'PPG BAR',
            'type': 'Marshalling',
            'location': 'Control Room Area 1',
            'rails': [
                {
                    'name': 'Rail_A_IS',
                    'position': 'Top',
                    'length': 2000.0,
                    'io_type': 'Analog',
                    'intrinsic': 'IS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'Barrier',
                    'reserved_from': 0.0,
                    'reserved_to': 50.0
                },
                {
                    'name': 'Rail_B_NIS',
                    'position': 'Middle',
                    'length': 1800.0,
                    'io_type': 'Digital',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'IOModule',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        }
    ]
    
    return cabinets

def test_enhanced_logging():
    """测试增强的日志记录功能"""
    print("🔧 增强日志记录测试")
    print("=" * 60)
    
    # 设置详细日志
    setup_detailed_logging()
    
    # 创建分配器
    config = {
        'allocation_settings': {
            'enable_parttype_matching': True,
            'enable_detailed_logging': True,
            'max_allocation_attempts': 1000
        },
        'naming_settings': {
            'rack_prefix': 'R',
            'enable_auto_naming': True
        }
    }
    
    try:
        allocator = IOAllocator(config)
        print("✅ 分配器创建成功")
        
        # 创建测试数据
        test_io_points = create_test_io_points()
        test_cabinets = create_test_cabinets()
        
        print(f"📋 准备测试 {len(test_io_points)} 个I/O点")
        print(f"🏢 使用 {len(test_cabinets)} 个测试机柜")
        
        # 为每个I/O点进行分配测试
        success_count = 0
        
        for i, io_point in enumerate(test_io_points, 1):
            print(f"\n{'='*40}")
            print(f"📍 测试 {i}/{len(test_io_points)}: {io_point.tag}")
            print(f"   - 信号类型: {io_point.signal_type.value}")
            print(f"   - 典型回路: {io_point.wiring_typical}")
            print(f"   - 本安要求: {'是' if io_point.is_intrinsic else '否'}")
            print(f"{'='*40}")
            
            # 选择合适的机柜（这里简化为使用第一个机柜）
            target_cabinet = test_cabinets[0]
            
            try:
                # 执行分配
                success = allocator._allocate_single_point(
                    io_point, 
                    target_cabinet, 
                    {}  # 空的典型回路字典，使用内部管理器
                )
                
                if success:
                    print(f"✅ {io_point.tag} 分配成功")
                    print(f"   - 最终状态: {io_point.allocation_status}")
                    print(f"   - 分配机柜: {io_point.allocated_cabinet}")
                    print(f"   - 分配导轨: {io_point.allocated_rail}")
                    
                    # 显示器件分配信息
                    if hasattr(io_point, 'component_allocations') and io_point.component_allocations:
                        print(f"   - 分配器件数: {len(io_point.component_allocations)}")
                        for j, allocation in enumerate(io_point.component_allocations, 1):
                            if hasattr(allocation, 'component_name') and hasattr(allocation, 'rail_name'):
                                print(f"     {j}. {allocation.component_name} -> {allocation.rail_name}")
                    
                    success_count += 1
                else:
                    print(f"❌ {io_point.tag} 分配失败")
                    
            except Exception as e:
                print(f"❌ {io_point.tag} 分配异常: {e}")
                import traceback
                traceback.print_exc()
        
        print(f"\n{'='*60}")
        print(f"📊 测试结果总结:")
        print(f"   - 成功分配: {success_count}/{len(test_io_points)} 个I/O点")
        print(f"   - 成功率: {success_count/len(test_io_points)*100:.1f}%")
        
        if success_count == len(test_io_points):
            print("🎉 所有I/O点分配成功！")
        else:
            print("⚠️  部分I/O点分配失败，请查看日志")
        
        print(f"\n📝 详细日志已保存到: enhanced_logging_test.log")
        print("🔍 日志特点:")
        print("   - 每条日志都包含I/O点标识 [I/O点: TAG]")
        print("   - 分配开始时显示典型回路信息")
        print("   - 器件分配过程详细记录")
        print("   - 错误和警告信息清晰标识")
        
        return success_count == len(test_io_points)
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_log_file():
    """分析生成的日志文件"""
    print(f"\n{'='*60}")
    print("📊 日志文件分析")
    
    try:
        with open('enhanced_logging_test.log', 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 统计不同类型的日志
        lines = log_content.split('\n')
        
        info_count = sum(1 for line in lines if ' - INFO - ' in line)
        debug_count = sum(1 for line in lines if ' - DEBUG - ' in line)
        warning_count = sum(1 for line in lines if ' - WARNING - ' in line)
        error_count = sum(1 for line in lines if ' - ERROR - ' in line)
        
        # 统计包含I/O点标识的日志
        io_point_logs = sum(1 for line in lines if '[I/O点:' in line)
        
        print(f"📈 日志统计:")
        print(f"   - 总日志行数: {len([l for l in lines if l.strip()])}")
        print(f"   - INFO级别: {info_count}")
        print(f"   - DEBUG级别: {debug_count}")
        print(f"   - WARNING级别: {warning_count}")
        print(f"   - ERROR级别: {error_count}")
        print(f"   - 包含I/O点标识的日志: {io_point_logs}")
        
        # 显示一些示例日志
        print(f"\n📋 日志示例:")
        example_logs = [line for line in lines if '[I/O点:' in line and ' - INFO - ' in line][:3]
        for i, log in enumerate(example_logs, 1):
            print(f"   {i}. {log}")
        
        if io_point_logs > 0:
            print("✅ 日志增强成功：所有分配相关日志都包含I/O点标识")
        else:
            print("❌ 日志增强失败：未找到包含I/O点标识的日志")
        
        return io_point_logs > 0
        
    except Exception as e:
        print(f"❌ 日志文件分析失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 I/O点分配增强日志记录测试")
    print("目标：验证每条日志都包含I/O点标识，提高可追踪性")
    print("=" * 70)
    
    # 执行测试
    test_success = test_enhanced_logging()
    
    # 分析日志文件
    log_analysis_success = analyze_log_file()
    
    print(f"\n{'='*70}")
    if test_success and log_analysis_success:
        print("🎉 增强日志记录测试完全成功！")
        print("✅ I/O点分配过程现在具有完整的可追踪性")
        print("✅ 每条日志都清楚标识了对应的I/O点")
        print("✅ 分配开始时显示关键信息（典型回路等）")
        print("✅ 错误和警告信息更加清晰")
    else:
        print("⚠️  测试发现问题:")
        print(f"   - 分配功能测试: {'✅' if test_success else '❌'}")
        print(f"   - 日志增强验证: {'✅' if log_analysis_success else '❌'}")
    
    print("\n📝 建议：在生产环境中使用时，可以根据需要调整日志级别")
    print("   - INFO级别：显示分配开始和结果")
    print("   - DEBUG级别：显示详细的分配过程")
