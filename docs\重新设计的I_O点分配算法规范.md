# 重新设计的I/O点分配算法规范

## 1. 核心业务规则澄清

### 1.1 器件位置分配粒度

#### 端子排分配
- **触发条件**: 典型回路中定义了`HardwareType="FieldTermIn"`的器件
- **分配策略**: 端子排（端子成组）分配，采用ETP和电缆两种分配策略
- **分配粒度**: 分配到具体的端子号
- **器件类型**: `FieldTermIn`代表一片端子

#### ETP卡件分配
- **分配目标**: 分配到具体的卡件（IOModule）的某个通道上
- **ETP与卡件关系**: ETP和卡件实际上是对应的
- **分配粒度**: 卡件的具体通道

### 1.2 机柜选择策略

#### 电缆完整性约束（核心规则）
- **同一电缆约束**: 同一电缆的所有I/O点必须在同一机柜 ✅ **不允许改动**
- **器件统一约束**: 同一个I/O点的所有器件必须位于同一机柜 ✅ **新增约束**
- **空间不足处理**: 如果某个器件在当前机柜的兼容导轨上没有空间，不可以选择其他机柜，应报告分配失败

### 1.3 器件数量计算

#### 典型回路XML解析规则
- **Count属性**: `Count="1"`表示每个I/O点需要1个该器件
- **ChannelCount**: 指该器件下一层的channel数量
- **导轨占用计算**: 只考虑`MarshallingCabinet`下第一层器件的`width`或`length`属性

#### 共享器件处理
- **ETP通道数量确定**:
  1. 优先读取`ChannelCount`属性
  2. 如果`ChannelCount`为空，读取下一层`slots`的`Names`属性
  3. 例如: `P1,P2,P3,P4,P5,P6,P7,P8,P9,P10,P11,P12,P13,P14,P15,P16` = 16个通道

### 1.4 分配结果存储

#### I/O点对象存储要求
- **存储范围**: 需要存储所有分配的器件信息
- **命名规则**: 按照标准命名规则命名所有器件
- **数据结构**: 支持多器件分配信息的完整存储

## 2. 重新设计的分配算法架构

### 2.1 新增数据模型

#### ComponentAllocation（器件分配结果）
```python
@dataclass
class ComponentAllocation:
    """单个器件的分配结果"""
    component_name: str          # 器件名称（如FTB1, CPM16-AI3700）
    component_type: str          # 器件类型（如FieldTermIn, ETP, Barrier）
    part_number: str             # 器件型号
    cabinet_name: str            # 实际机柜名称（如E-1001）
    rail_name: str               # 导轨名称（如Rail_A）
    position: str                # 在导轨上的位置
    channel: Optional[int]       # 通道号（如果适用）
    terminal_numbers: List[str]  # 端子号列表（如果是端子排）
```

#### ActualCabinet（实际机柜）
```python
@dataclass
class ActualCabinet:
    """实际机柜实例"""
    name: str                    # 实际机柜名称（如E-1001）
    template_name: str           # 典型机柜模板名称（如PPG BAR）
    location: str                # 机柜位置
    rails: List[RailInstance]    # 导轨实例列表
    available_space: Dict[str, float]  # 各导轨可用空间
```

#### ComponentCompatibility（器件兼容性）
```python
@dataclass
class ComponentCompatibility:
    """器件与导轨的兼容性"""
    component_type: str          # 器件类型
    compatible_rails: List[str]  # 兼容的导轨类型列表
    space_requirement: float     # 空间需求（mm）
    mounting_type: str           # 安装方式（Rail/Rack）
```

### 2.2 核心分配引擎

#### ComponentAllocationEngine
```python
class ComponentAllocationEngine:
    """基于典型回路的器件分配引擎"""
    
    def allocate_io_point_components(self, 
                                   io_point: IOPoint,
                                   actual_cabinets: List[ActualCabinet],
                                   wiring_typical: WiringTypical) -> List[ComponentAllocation]:
        """
        为单个I/O点分配所有必需的器件
        
        步骤：
        1. 解析典型回路XML，提取MarshallingCabinet下的器件清单
        2. 为每个器件确定兼容的导轨类型
        3. 在目标机柜中查找可用的导轨空间
        4. 分配器件到具体位置（导轨位置、通道、端子等）
        5. 更新导轨空间占用情况
        """
```

### 2.3 分配流程重新设计

#### 主分配流程
```python
def allocate_io_points(self, cables, actual_cabinets, wiring_typicals):
    """
    重新设计的主分配流程
    
    1. 遍历每条电缆
    2. 为电缆选择目标实际机柜
    3. 为电缆中的每个I/O点：
       a. 解析其典型回路，获取器件清单
       b. 检查所有器件是否能在目标机柜中分配
       c. 如果可以，执行器件分配
       d. 如果不可以，报告整条电缆分配失败
    4. 更新I/O点的多器件分配信息
    """
```

## 3. 需要进一步澄清的问题

### 3.1 器件命名规则
- **端子排命名**: 如何命名分配的端子排？是否有标准格式？
- **ETP命名**: ETP卡件的命名规则是什么？
- **通道命名**: 卡件通道的命名格式是什么？

### 3.2 空间计算细节
- **导轨空间单位**: 导轨长度和器件宽度都是以mm为单位吗？
- **空间预留**: 器件之间是否需要预留间隙？
- **空间冲突处理**: 如何处理导轨空间不足的情况？

### 3.3 分配策略细节
- **器件分配顺序**: 多个器件的分配顺序是否有要求？
- **导轨选择优先级**: 如果多个导轨都兼容某个器件，选择哪个？
- **通道分配策略**: ETP卡件的通道分配是否有特定规则？

### 3.4 错误处理
- **部分分配失败**: 如果I/O点的某个器件分配失败，其他器件是否回滚？
- **电缆分配失败**: 如果电缆中某个I/O点分配失败，整条电缆是否回滚？
- **机柜容量不足**: 如何报告和处理机柜容量不足的情况？

## 4. 实施优先级

### 第一阶段：数据模型和解析器
1. 创建新的数据模型（ComponentAllocation, ActualCabinet等）
2. 增强典型回路XML解析器，提取器件清单
3. 实现PIDB cabinet工作表解析，获取实际机柜信息
4. 建立器件与导轨的兼容性映射

### 第二阶段：核心分配算法
1. 重写`_allocate_single_point`方法，支持多器件分配
2. 重写`_select_target_cabinet`方法，使用实际机柜
3. 实现器件兼容性检查和空间计算
4. 实现器件位置分配逻辑

### 第三阶段：集成和测试
1. 集成新的分配引擎到现有系统
2. 更新GUI显示，支持多器件分配结果
3. 更新导出功能，包含完整的器件分配信息
4. 全面测试和验证

## 5. 预期改进效果

### 5.1 业务准确性
- ✅ 严格按照典型回路定义进行器件分配
- ✅ 正确处理器件与导轨的兼容性约束
- ✅ 使用实际机柜实例而非模板名称

### 5.2 分配结果完整性
- ✅ 每个I/O点包含完整的多器件分配信息
- ✅ 准确的器件位置、通道、端子信息
- ✅ 符合实际工程需求的分配结果

### 5.3 系统可靠性
- ✅ 严格的约束检查，避免无效分配
- ✅ 完整的错误处理和回滚机制
- ✅ 详细的分配失败原因报告
