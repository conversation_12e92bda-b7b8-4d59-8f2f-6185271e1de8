#!/usr/bin/env python3
"""
完整修复验证测试
验证I/O点分配系统的所有修复是否正确工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_complete_system_fix():
    """测试完整系统修复"""
    print("=== 完整I/O点分配系统修复验证 ===")
    
    try:
        # 1. 加载配置和数据
        from utils.config_manager_simple import ConfigManager
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        data_loader = DataLoader(config)
        
        print("✓ 系统初始化成功")
        
        # 2. 验证路径配置修复
        print(f"\n=== 路径配置修复验证 ===")
        cabinet_path = config_manager.get('data_paths.cabinet_profiles')
        wiring_path = config_manager.get('data_paths.wiring_typical')
        iodb_path = config_manager.get('data_paths.iodb')
        
        path_correct = all([
            "01B_Cabinet Templates" in cabinet_path,
            "01C_Wiring Typical" in wiring_path,
            "04A_IODB" in iodb_path
        ])
        
        if path_correct:
            print("✓ 配置路径修复成功")
        else:
            print("❌ 配置路径修复失败")
            return False
        
        # 3. 验证数据加载修复
        print(f"\n=== 数据加载修复验证 ===")
        cabinet_profiles = data_loader.load_cabinet_profiles()
        wiring_typicals = data_loader.load_wiring_typicals()
        
        # 加载IODB数据
        iodb_path_obj = Path(iodb_path)
        iodb_files = list(iodb_path_obj.glob("*.xlsx"))
        if not iodb_files:
            print("❌ IODB文件加载失败")
            return False
        
        iodb_data = data_loader.load_iodb_data(str(iodb_files[0]))
        models = data_loader.create_data_models(
            iodb_data, {}, cabinet_profiles, wiring_typicals
        )
        
        data_load_success = all([
            len(cabinet_profiles) > 0,
            len(wiring_typicals) > 0,
            len(models['io_points']) > 0,
            len(models['cables']) > 0
        ])
        
        if data_load_success:
            print(f"✓ 数据加载修复成功: {len(models['io_points'])}点, {len(models['cables'])}缆")
        else:
            print("❌ 数据加载修复失败")
            return False
        
        # 4. 验证分配算法修复
        print(f"\n=== 分配算法修复验证 ===")
        allocator = IOAllocator(config)
        
        allocation_result = allocator.allocate_io_points(
            models['cables'],
            models['cabinets'],
            {wt.name: {'components': []} for wt in models['wiring_typicals']}
        )
        
        # 检查分配结果
        regular_allocated = 0
        spare_reserved = 0
        unallocated = 0
        
        for point in allocation_result.allocated_points:
            if hasattr(point, 'is_spare') and point.is_spare:
                if point.allocation_status == "Spare预留":
                    spare_reserved += 1
            else:
                if point.allocation_status == "已分配":
                    regular_allocated += 1
                elif point.allocation_status == "未分配":
                    unallocated += 1
        
        allocation_success = all([
            regular_allocated == len(models['io_points']),  # 所有常规点都已分配
            unallocated == 0,  # 没有未分配的点
            spare_reserved > 0,  # 生成了spare点
            len(allocation_result.failed_points) == 0  # 没有失败的点
        ])
        
        if allocation_success:
            print(f"✓ 分配算法修复成功:")
            print(f"  - 常规点已分配: {regular_allocated}/{len(models['io_points'])}")
            print(f"  - Spare点预留: {spare_reserved}")
            print(f"  - 未分配点: {unallocated}")
        else:
            print("❌ 分配算法修复失败")
            return False
        
        # 5. 验证状态更新修复
        print(f"\n=== 状态更新修复验证 ===")
        
        # 检查常规I/O点的状态和分配信息
        status_update_success = True
        incomplete_points = 0
        
        for point in models['io_points'][:10]:  # 检查前10个点
            if point.allocation_status != "已分配":
                status_update_success = False
                break
            
            if not all([point.allocated_cabinet, point.allocated_rack, point.allocated_slot]):
                incomplete_points += 1
        
        if status_update_success and incomplete_points == 0:
            print("✓ I/O点状态更新修复成功")
        else:
            print(f"❌ I/O点状态更新修复失败: 不完整点数={incomplete_points}")
            return False
        
        # 6. 验证统计计算修复
        print(f"\n=== 统计计算修复验证 ===")
        
        if allocation_result.summary:
            summary = allocation_result.summary
            
            # 验证成功率计算
            expected_rate = 100.0  # 所有常规点都应该分配成功
            actual_rate = summary.get('success_rate', 0)
            
            # 验证统计数据
            stats_correct = all([
                summary.get('total_points') == len(models['io_points']),
                summary.get('allocated_count') == len(models['io_points']),
                summary.get('failed_count') == 0,
                abs(actual_rate - expected_rate) < 0.1
            ])
            
            if stats_correct:
                print(f"✓ 统计计算修复成功:")
                print(f"  - 总点数: {summary.get('total_points')}")
                print(f"  - 成功分配: {summary.get('allocated_count')}")
                print(f"  - 成功率: {actual_rate:.1f}%")
            else:
                print("❌ 统计计算修复失败")
                return False
        else:
            print("❌ 统计数据缺失")
            return False
        
        # 7. 验证GUI显示修复
        print(f"\n=== GUI显示修复验证 ===")
        
        # 模拟GUI导出数据准备
        all_points = allocation_result.allocated_points + allocation_result.failed_points
        export_data = []
        
        for io_point in all_points[:5]:  # 检查前5个点
            export_data.append({
                'Tag': io_point.tag,
                'Status': io_point.allocation_status,
                'AllocatedCabinet': io_point.allocated_cabinet or '',
                'AllocatedRack': io_point.allocated_rack or '',
                'AllocatedSlot': io_point.allocated_slot or ''
            })
        
        # 验证导出数据的完整性
        gui_data_complete = all([
            point['Status'] in ['已分配', 'Spare预留'] for point in export_data
        ])
        
        if gui_data_complete:
            print("✓ GUI显示数据修复成功")
            for point in export_data[:3]:  # 显示前3个点
                print(f"  - {point['Tag']}: {point['Status']} -> {point['AllocatedCabinet']}")
        else:
            print("❌ GUI显示数据修复失败")
            return False
        
        # 8. 验证用户报告问题的解决
        print(f"\n=== 用户问题解决验证 ===")
        
        # 原问题：43 cables and 128 I/O points total, but 0 points were successfully allocated and 0 failed, yet it shows a 10000.0% success rate
        
        current_stats = {
            'cables': len(models['cables']),
            'total_points': len(models['io_points']),
            'allocated_points': regular_allocated,
            'failed_points': len(allocation_result.failed_points),
            'success_rate': summary.get('success_rate', 0)
        }
        
        problem_solved = all([
            current_stats['cables'] > 0,  # 电缆数量正常
            current_stats['total_points'] > 0,  # I/O点数量正常
            current_stats['allocated_points'] > 0,  # 有成功分配的点
            current_stats['success_rate'] <= 100,  # 成功率正常
            current_stats['success_rate'] > 0  # 成功率不为0
        ])
        
        if problem_solved:
            print("✓ 用户报告问题已解决:")
            print(f"  - 电缆数: {current_stats['cables']}")
            print(f"  - 总I/O点数: {current_stats['total_points']}")
            print(f"  - 成功分配: {current_stats['allocated_points']}")
            print(f"  - 分配失败: {current_stats['failed_points']}")
            print(f"  - 成功率: {current_stats['success_rate']:.1f}%")
        else:
            print("❌ 用户报告问题未完全解决")
            return False
        
        # 9. 最终验证总结
        print(f"\n=== 最终验证总结 ===")
        
        all_fixes_successful = all([
            path_correct,
            data_load_success,
            allocation_success,
            status_update_success,
            stats_correct,
            gui_data_complete,
            problem_solved
        ])
        
        if all_fixes_successful:
            print("🎉 所有修复验证通过！")
            print("\n修复内容总结:")
            print("✅ 配置路径修复 - 正确指向实际文件夹")
            print("✅ 数据加载修复 - 正确加载所有配置和数据文件")
            print("✅ 分配算法修复 - 正确分配所有I/O点")
            print("✅ 状态更新修复 - 正确更新I/O点分配状态")
            print("✅ 统计计算修复 - 正确计算成功率和统计数据")
            print("✅ GUI显示修复 - 正确显示分配结果和状态")
            print("✅ 用户问题解决 - 消除异常成功率显示")
        else:
            print("❌ 部分修复验证失败")
        
        return all_fixes_successful
        
    except Exception as e:
        print(f"\n✗ 验证过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_system_fix()
    if success:
        print("\n🎉 I/O点分配系统修复验证完成，所有问题已解决！")
        print("\n系统现在可以:")
        print("• 正确加载机柜配置和典型回路")
        print("• 准确分配所有I/O点到合适位置")
        print("• 正确更新I/O点的分配状态")
        print("• 准确计算和显示分配统计信息")
        print("• 在GUI中正确显示分配结果")
        print("• 生成完整准确的导出报告")
    else:
        print("\n❌ 修复验证发现问题，需要进一步检查。")
