"""
Wire连接信息管理器
负责创建和管理I/O点的Wire连接信息
"""

from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field

from core.logger import get_logger
from core.data_models import IOPoint
from core.wiring_typical_parser import WiringTypicalManager
from core.component_allocation_models import ComponentAllocation


@dataclass
class WireConnection:
    """Wire连接信息"""
    wire_name: str                             # Wire名称
    io_point_tag: str                          # I/O点Tag
    end1: str                                  # 起始点
    end2: str                                  # 终点
    end1_screw_id: str                         # 起始点ScrewID
    end2_screw_id: str                         # 终点ScrewID
    end1_naming_convention: str                # 起始点命名约定
    end2_naming_convention: str                # 终点命名约定
    color: str = ""                            # 线缆颜色
    wire_type: str = ""                        # 线缆类型
    insulation: str = ""                       # 绝缘类型
    
    # 实际分配信息
    end1_actual_name: str = ""                 # 起始点实际器件名称
    end2_actual_name: str = ""                 # 终点实际器件名称
    end1_cabinet: str = ""                     # 起始点机柜
    end2_cabinet: str = ""                     # 终点机柜
    
    def to_csv_dict(self) -> Dict[str, Any]:
        """转换为CSV导出格式"""
        return {
            'wire_name': self.wire_name,
            'io_point_tag': self.io_point_tag,
            'end1': self.end1,
            'end2': self.end2,
            'end1_screw_id': self.end1_screw_id,
            'end2_screw_id': self.end2_screw_id,
            'end1_actual_name': self.end1_actual_name,
            'end2_actual_name': self.end2_actual_name,
            'end1_cabinet': self.end1_cabinet,
            'end2_cabinet': self.end2_cabinet,
            'color': self.color,
            'wire_type': self.wire_type,
            'insulation': self.insulation
        }


class WireConnectionManager:
    """Wire连接信息管理器"""

    def __init__(self, typical_manager: WiringTypicalManager = None,
                 wiring_typical_directory: str = "01C_Wiring Typical"):
        self.logger = get_logger(__name__)

        # 优先使用传入的典型回路管理器，避免重复创建
        if typical_manager is not None:
            self.typical_manager = typical_manager
            self.wiring_typicals = typical_manager.load_all_typicals()
            self.logger.debug("使用共享的典型回路管理器")
        else:
            # 兼容性：如果没有传入管理器，则创建新的
            self.typical_manager = WiringTypicalManager(wiring_typical_directory)
            self.wiring_typicals = self.typical_manager.load_all_typicals()
            self.logger.debug("创建新的典型回路管理器")
    
    def create_wire_connections_for_io_point(self, 
                                           io_point: IOPoint,
                                           component_allocations: List[ComponentAllocation]) -> List[WireConnection]:
        """
        为I/O点创建Wire连接信息
        
        Args:
            io_point: I/O点对象
            component_allocations: 器件分配结果列表
            
        Returns:
            Wire连接信息列表
        """
        wire_connections = []
        
        try:
            # 获取I/O点的典型回路
            typical_name = getattr(io_point, 'wiring_typical', None)
            if not typical_name:
                self.logger.warning(f"I/O点 {io_point.tag} 没有典型回路信息")
                return wire_connections
            
            typical_def = self.typical_manager.get_typical_by_name(typical_name)
            if not typical_def:
                self.logger.warning(f"未找到典型回路: {typical_name}")
                return wire_connections
            
            # 创建器件名称映射
            component_name_map = self._create_component_name_mapping(component_allocations)
            
            # 为每个Wire创建连接信息
            for wire_info in typical_def.wires:
                wire_connection = self._create_wire_connection(
                    wire_info, io_point, component_name_map
                )
                if wire_connection:
                    wire_connections.append(wire_connection)
            
            self.logger.debug(f"为I/O点 {io_point.tag} 创建了 {len(wire_connections)} 个Wire连接")
            return wire_connections
            
        except Exception as e:
            self.logger.error(f"为I/O点 {io_point.tag} 创建Wire连接失败: {e}")
            return wire_connections
    
    def _create_component_name_mapping(self, 
                                     component_allocations: List[ComponentAllocation]) -> Dict[str, ComponentAllocation]:
        """创建器件名称到分配结果的映射"""
        name_map = {}
        
        for allocation in component_allocations:
            # 使用器件定义中的名称作为键
            component_def_name = allocation.component_definition.name
            name_map[component_def_name] = allocation
        
        return name_map
    
    def _create_wire_connection(self, 
                              wire_info: Dict[str, Any],
                              io_point: IOPoint,
                              component_name_map: Dict[str, ComponentAllocation]) -> Optional[WireConnection]:
        """
        创建单个Wire连接信息
        
        Args:
            wire_info: Wire信息字典
            io_point: I/O点对象
            component_name_map: 器件名称映射
            
        Returns:
            Wire连接信息
        """
        try:
            wire_name = wire_info.get('name', '')
            end1 = wire_info.get('end1', '')
            end2 = wire_info.get('end2', '')
            
            # 解析End1和End2中的器件名称
            end1_component_name = self._extract_component_name_from_end(end1)
            end2_component_name = self._extract_component_name_from_end(end2)
            
            # 获取实际的器件分配信息
            end1_allocation = component_name_map.get(end1_component_name)
            end2_allocation = component_name_map.get(end2_component_name)
            
            wire_connection = WireConnection(
                wire_name=wire_name,
                io_point_tag=io_point.tag,
                end1=end1,
                end2=end2,
                end1_screw_id=wire_info.get('end1_screw_id', ''),
                end2_screw_id=wire_info.get('end2_screw_id', ''),
                end1_naming_convention=wire_info.get('end1_naming_convention', ''),
                end2_naming_convention=wire_info.get('end2_naming_convention', ''),
                color=wire_info.get('color', ''),
                wire_type=wire_info.get('wire_type', ''),
                insulation=wire_info.get('insulation', ''),
                
                # 填充实际分配信息
                end1_actual_name=end1_allocation.component_name if end1_allocation else '',
                end2_actual_name=end2_allocation.component_name if end2_allocation else '',
                end1_cabinet=end1_allocation.cabinet_name if end1_allocation else '',
                end2_cabinet=end2_allocation.cabinet_name if end2_allocation else ''
            )
            
            return wire_connection
            
        except Exception as e:
            self.logger.warning(f"创建Wire连接失败 {wire_info.get('name', 'Unknown')}: {e}")
            return None
    
    def _extract_component_name_from_end(self, end_path: str) -> str:
        """
        从End路径中提取器件名称
        
        例如: "FTB1.Channels.Terminals.F+" -> "FTB1"
        
        Args:
            end_path: End路径字符串
            
        Returns:
            器件名称
        """
        if not end_path:
            return ''
        
        # 取第一个.之前的部分作为器件名称
        first_dot_index = end_path.find('.')
        if first_dot_index != -1:
            return end_path[:first_dot_index]
        else:
            return end_path
    
    def export_wire_connections_to_csv(self, 
                                     wire_connections: List[WireConnection],
                                     output_file: str) -> bool:
        """
        导出Wire连接信息到CSV文件
        
        Args:
            wire_connections: Wire连接信息列表
            output_file: 输出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            import pandas as pd
            
            # 转换为字典列表
            data = [conn.to_csv_dict() for conn in wire_connections]
            
            # 创建DataFrame并导出
            df = pd.DataFrame(data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"Wire连接信息导出成功: {output_file} ({len(wire_connections)}条记录)")
            return True
            
        except Exception as e:
            self.logger.error(f"导出Wire连接信息失败: {e}")
            return False
    
    def batch_create_wire_connections(self, 
                                    io_points: List[IOPoint]) -> Dict[str, List[WireConnection]]:
        """
        批量创建I/O点的Wire连接信息
        
        Args:
            io_points: I/O点列表
            
        Returns:
            I/O点Tag到Wire连接列表的映射
        """
        all_wire_connections = {}
        
        for io_point in io_points:
            try:
                # 获取I/O点的器件分配信息
                component_allocations = getattr(io_point, 'component_allocations', [])
                
                if component_allocations:
                    wire_connections = self.create_wire_connections_for_io_point(
                        io_point, component_allocations
                    )
                    all_wire_connections[io_point.tag] = wire_connections
                    
                    # 将Wire连接信息存储到I/O点对象中
                    io_point.wire_connections = [conn.to_csv_dict() for conn in wire_connections]
                
            except Exception as e:
                self.logger.error(f"为I/O点 {io_point.tag} 批量创建Wire连接失败: {e}")
        
        total_connections = sum(len(conns) for conns in all_wire_connections.values())
        self.logger.info(f"批量创建Wire连接完成: {len(all_wire_connections)}个I/O点, {total_connections}个连接")
        
        return all_wire_connections
