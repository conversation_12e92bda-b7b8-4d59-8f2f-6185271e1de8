#!/usr/bin/env python3
"""
测试I/O点状态更新
验证分配后I/O点的状态字段是否正确更新
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_io_point_status_update():
    """测试I/O点状态更新"""
    print("=== 测试I/O点状态更新 ===")
    
    try:
        # 1. 加载配置和数据
        from utils.config_manager_simple import ConfigManager
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        data_loader = DataLoader(config)
        
        # 加载所有数据
        cabinet_profiles = data_loader.load_cabinet_profiles()
        wiring_typicals = data_loader.load_wiring_typicals()
        
        # 加载IODB数据
        iodb_path = Path(config_manager.get('data_paths.iodb', ''))
        iodb_files = list(iodb_path.glob("*.xlsx"))
        if not iodb_files:
            print("❌ 未找到IODB文件")
            return False
        
        iodb_data = data_loader.load_iodb_data(str(iodb_files[0]))
        
        # 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            {},
            cabinet_profiles,
            wiring_typicals
        )
        
        print(f"✓ 数据加载完成: {len(models['io_points'])}个I/O点")
        
        # 2. 检查分配前的状态
        print(f"\n=== 分配前状态检查 ===")
        unallocated_count = 0
        for point in models['io_points'][:5]:  # 检查前5个点
            print(f"  {point.tag}: 状态='{point.allocation_status}', 机柜='{point.allocated_cabinet}'")
            if point.allocation_status == "未分配":
                unallocated_count += 1
        
        print(f"  前5个点中未分配数量: {unallocated_count}")
        
        # 3. 执行分配
        print(f"\n=== 执行I/O点分配 ===")
        allocator = IOAllocator(config)
        
        allocation_result = allocator.allocate_io_points(
            models['cables'],
            models['cabinets'],
            {wt.name: {'components': []} for wt in models['wiring_typicals']}
        )
        
        print(f"✓ 分配执行完成")
        print(f"  - 分配成功点数: {len(allocation_result.allocated_points)}")
        print(f"  - 分配失败点数: {len(allocation_result.failed_points)}")
        
        # 4. 检查分配后的状态
        print(f"\n=== 分配后状态检查 ===")
        
        # 检查常规I/O点的状态
        regular_allocated = 0
        regular_unallocated = 0
        spare_allocated = 0
        spare_reserved = 0
        
        print("  常规I/O点状态:")
        for i, point in enumerate(models['io_points'][:5]):  # 检查前5个常规点
            print(f"    {point.tag}:")
            print(f"      状态: '{point.allocation_status}'")
            print(f"      分配机柜: '{point.allocated_cabinet}'")
            print(f"      分配机架: '{point.allocated_rack}'")
            print(f"      分配槽位: '{point.allocated_slot}'")
            
            if point.allocation_status == "已分配":
                regular_allocated += 1
            elif point.allocation_status == "未分配":
                regular_unallocated += 1
        
        # 检查所有常规I/O点（重置计数器，避免重复计算）
        regular_allocated = 0
        regular_unallocated = 0
        for point in models['io_points']:
            if point.allocation_status == "已分配":
                regular_allocated += 1
            elif point.allocation_status == "未分配":
                regular_unallocated += 1
        
        print(f"  常规I/O点统计:")
        print(f"    已分配: {regular_allocated}")
        print(f"    未分配: {regular_unallocated}")
        
        # 检查spare点的状态
        print(f"\n  Spare点状态:")
        spare_points = [p for p in allocation_result.allocated_points if hasattr(p, 'is_spare') and p.is_spare]
        
        for i, point in enumerate(spare_points[:5]):  # 检查前5个spare点
            print(f"    {point.tag}:")
            print(f"      状态: '{point.allocation_status}'")
            print(f"      是否spare: {point.is_spare}")
            print(f"      电缆: '{point.cable_name}'")
            
            if point.allocation_status == "Spare预留":
                spare_reserved += 1
            elif point.allocation_status == "已分配":
                spare_allocated += 1
        
        # 统计所有spare点（重置计数器，避免重复计算）
        spare_reserved = 0
        spare_allocated = 0
        for point in spare_points:
            if point.allocation_status == "Spare预留":
                spare_reserved += 1
            elif point.allocation_status == "已分配":
                spare_allocated += 1
        
        print(f"  Spare点统计:")
        print(f"    预留状态: {spare_reserved}")
        print(f"    已分配状态: {spare_allocated}")
        print(f"    总spare点数: {len(spare_points)}")
        
        # 5. 验证数据一致性
        print(f"\n=== 数据一致性验证 ===")
        
        expected_regular = len(models['io_points'])
        expected_spare = len(spare_points)
        total_expected = expected_regular + expected_spare
        total_allocated = len(allocation_result.allocated_points)
        
        print(f"  期望常规点数: {expected_regular}")
        print(f"  期望spare点数: {expected_spare}")
        print(f"  期望总点数: {total_expected}")
        print(f"  实际分配点数: {total_allocated}")
        print(f"  数量一致性: {'✓' if total_expected == total_allocated else '❌'}")
        
        # 6. 检查状态更新的正确性
        print(f"\n=== 状态更新正确性检查 ===")
        
        status_correct = True
        
        # 检查常规点是否都被标记为"已分配"
        if regular_unallocated > 0:
            print(f"  ❌ 有 {regular_unallocated} 个常规I/O点状态未更新")
            status_correct = False
        else:
            print(f"  ✓ 所有常规I/O点状态已正确更新")
        
        # 检查spare点是否都被标记为"Spare预留"
        if spare_reserved != len(spare_points):
            print(f"  ❌ Spare点状态更新不完整: {spare_reserved}/{len(spare_points)}")
            status_correct = False
        else:
            print(f"  ✓ 所有Spare点状态已正确设置")
        
        # 7. 检查分配信息的完整性
        print(f"\n=== 分配信息完整性检查 ===")
        
        info_complete = True
        incomplete_points = []
        
        for point in models['io_points'][:10]:  # 检查前10个常规点
            if point.allocation_status == "已分配":
                if not point.allocated_cabinet or not point.allocated_rack:
                    incomplete_points.append(point.tag)
                    info_complete = False
        
        if info_complete:
            print(f"  ✓ 分配信息完整")
        else:
            print(f"  ❌ 有 {len(incomplete_points)} 个点的分配信息不完整")
            for tag in incomplete_points[:3]:  # 显示前3个
                print(f"    - {tag}")
        
        # 8. 最终结果
        print(f"\n=== 最终测试结果 ===")
        
        all_tests_passed = True
        
        if regular_unallocated > 0:
            print(f"❌ 常规I/O点状态更新失败")
            all_tests_passed = False
        else:
            print(f"✓ 常规I/O点状态更新成功")
        
        if spare_reserved != len(spare_points):
            print(f"❌ Spare点状态设置失败")
            all_tests_passed = False
        else:
            print(f"✓ Spare点状态设置成功")
        
        if not info_complete:
            print(f"❌ 分配信息不完整")
            all_tests_passed = False
        else:
            print(f"✓ 分配信息完整")
        
        if all_tests_passed:
            print(f"\n🎉 所有测试通过！I/O点状态更新正常工作。")
        else:
            print(f"\n❌ 部分测试失败，需要进一步修复。")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"\n✗ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_io_point_status_update()
    if success:
        print("\n✅ I/O点状态更新测试完成，所有功能正常！")
    else:
        print("\n❌ I/O点状态更新测试发现问题，需要进一步检查。")
