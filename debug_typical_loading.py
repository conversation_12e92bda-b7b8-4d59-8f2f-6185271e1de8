"""
典型回路加载调试脚本
用于验证修复效果和诊断问题
"""

import logging
from pathlib import Path
from core.logger import setup_logging
from core.wiring_typical_parser import WiringTypicalManager
from core.component_allocation_engine import ComponentAllocationEngine

def setup_debug_logging():
    """设置调试级别的日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('debug_typical_loading.log', mode='w', encoding='utf-8')
        ]
    )

def test_typical_loading():
    """测试典型回路加载"""
    print("=== 典型回路加载测试 ===")
    
    # 设置调试日志
    setup_debug_logging()
    
    # 检查目录是否存在
    typical_dir = Path("01C_Wiring Typical")
    if not typical_dir.exists():
        print(f"❌ 典型回路目录不存在: {typical_dir}")
        return False
    
    # 列出所有XML文件
    xml_files = list(typical_dir.glob("*.xml"))
    print(f"📁 找到 {len(xml_files)} 个XML文件:")
    for xml_file in xml_files:
        print(f"   - {xml_file.name}")
    
    print("\n=== 测试1: 直接使用WiringTypicalManager ===")
    try:
        manager = WiringTypicalManager("01C_Wiring Typical")
        typicals = manager.load_all_typicals()
        
        print(f"✅ 成功加载 {len(typicals)} 个典型回路")
        
        # 显示每个典型回路的器件数量
        for name, typical in typicals.items():
            component_count = len(typical.marshalling_components)
            print(f"   - {name}: {component_count} 个汇控柜器件")
            
            if component_count == 0:
                print(f"     ⚠️  警告: {name} 没有汇控柜器件")
        
    except Exception as e:
        print(f"❌ WiringTypicalManager测试失败: {e}")
        return False
    
    print("\n=== 测试2: 使用ComponentAllocationEngine ===")
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True,
                'max_allocation_attempts': 1000
            }
        }
        
        engine = ComponentAllocationEngine(config, "01C_Wiring Typical")
        
        print(f"✅ ComponentAllocationEngine初始化成功")
        print(f"   - 典型回路数量: {len(engine.wiring_typicals)}")
        print(f"   - Wire管理器已初始化: {engine.wire_manager is not None}")
        
    except Exception as e:
        print(f"❌ ComponentAllocationEngine测试失败: {e}")
        return False
    
    print("\n=== 测试3: 检查具体的典型回路内容 ===")
    try:
        # 检查一个具体的典型回路
        test_typical_name = "AI IS BABP"  # 或者使用实际存在的名称
        
        if test_typical_name in typicals:
            typical = typicals[test_typical_name]
            print(f"📋 典型回路 '{test_typical_name}' 详情:")
            print(f"   - 信号类型: {typical.signal_type}")
            print(f"   - 本安类型: {typical.intrinsic}")
            print(f"   - 汇控柜器件数: {len(typical.marshalling_components)}")
            print(f"   - 系统柜器件数: {len(typical.system_components)}")
            print(f"   - Wire数量: {len(typical.wires)}")
            
            # 显示器件详情
            for i, component in enumerate(typical.marshalling_components[:3]):  # 只显示前3个
                print(f"   - 器件{i+1}: {component.name} ({component.component_type.value})")
                
        else:
            available_names = list(typicals.keys())[:5]  # 显示前5个可用名称
            print(f"⚠️  典型回路 '{test_typical_name}' 不存在")
            print(f"   可用的典型回路: {available_names}")
        
    except Exception as e:
        print(f"❌ 典型回路内容检查失败: {e}")
        return False
    
    print("\n=== 测试完成 ===")
    print("✅ 所有测试通过！")
    print("📝 详细日志已保存到 debug_typical_loading.log")
    
    return True

def check_xml_files():
    """检查XML文件的基本结构"""
    print("\n=== XML文件结构检查 ===")
    
    typical_dir = Path("01C_Wiring Typical")
    xml_files = list(typical_dir.glob("*.xml"))
    
    import xml.etree.ElementTree as ET
    
    for xml_file in xml_files:
        try:
            tree = ET.parse(xml_file)
            root = tree.getroot()
            
            # 检查基本结构
            name = root.get('Name', 'Unknown')
            marshalling_cabinet = root.find(".//ProfileComponent[@Name='MarshallingCabinet']")
            
            if marshalling_cabinet is not None:
                components = marshalling_cabinet.findall("./Components/ProfileComponent")
                print(f"✅ {xml_file.name}: MarshallingCabinet存在，{len(components)}个器件")
            else:
                print(f"❌ {xml_file.name}: 未找到MarshallingCabinet节点")
                
                # 列出所有ProfileComponent
                all_components = root.findall(".//ProfileComponent")
                component_names = [comp.get('Name', 'Unnamed') for comp in all_components[:5]]
                print(f"   可用的ProfileComponent: {component_names}")
                
        except Exception as e:
            print(f"❌ {xml_file.name}: XML解析失败 - {e}")

if __name__ == "__main__":
    print("🔧 典型回路加载问题诊断工具")
    print("=" * 50)
    
    # 首先检查XML文件结构
    check_xml_files()
    
    # 然后测试加载过程
    success = test_typical_loading()
    
    if success:
        print("\n🎉 诊断完成：系统工作正常！")
    else:
        print("\n⚠️  诊断完成：发现问题，请查看日志文件")
