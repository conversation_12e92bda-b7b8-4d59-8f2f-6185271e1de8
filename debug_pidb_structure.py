#!/usr/bin/env python3
"""
调试PIDB文件结构
检查实际PIDB文件的工作表和数据结构
"""

import sys
import os
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_pidb_file_structure():
    """调试PIDB文件结构"""
    print("=== 调试PIDB文件结构 ===")
    
    try:
        # 查找PIDB文件
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        print(f"✅ 找到PIDB文件: {pidb_file}")
        
        # 读取Excel文件的所有工作表
        excel_file = pd.ExcelFile(pidb_file)
        sheet_names = excel_file.sheet_names
        
        print(f"\n📋 PIDB文件包含的工作表:")
        for i, sheet_name in enumerate(sheet_names, 1):
            print(f"  {i}. {sheet_name}")
        
        # 检查是否有cabinet相关的工作表
        cabinet_sheets = []
        for sheet_name in sheet_names:
            if 'cabinet' in sheet_name.lower():
                cabinet_sheets.append(sheet_name)
        
        if cabinet_sheets:
            print(f"\n✅ 找到cabinet相关工作表: {cabinet_sheets}")
            
            # 分析每个cabinet工作表
            for sheet_name in cabinet_sheets:
                print(f"\n--- 分析工作表: {sheet_name} ---")
                try:
                    df = pd.read_excel(pidb_file, sheet_name=sheet_name)
                    print(f"行数: {len(df)}")
                    print(f"列数: {len(df.columns)}")
                    print(f"列名: {list(df.columns)}")
                    
                    # 显示前几行数据
                    if len(df) > 0:
                        print(f"\n前3行数据:")
                        print(df.head(3).to_string())
                    
                except Exception as e:
                    print(f"❌ 读取工作表 {sheet_name} 失败: {e}")
        else:
            print(f"\n❌ 未找到cabinet相关工作表")
            print(f"可能的原因:")
            print(f"  1. 工作表名称不包含'cabinet'")
            print(f"  2. 机柜数据在其他工作表中")
            
            # 检查每个工作表的内容
            print(f"\n🔍 检查所有工作表的内容:")
            for sheet_name in sheet_names:
                try:
                    df = pd.read_excel(pidb_file, sheet_name=sheet_name, nrows=5)
                    print(f"\n--- 工作表: {sheet_name} ---")
                    print(f"列名: {list(df.columns)}")
                    
                    # 检查是否包含机柜相关的列
                    cabinet_related_columns = []
                    for col in df.columns:
                        col_lower = str(col).lower()
                        if any(keyword in col_lower for keyword in ['cabinet', 'template', 'ppg', 'sys', 'bar', 'rio']):
                            cabinet_related_columns.append(col)
                    
                    if cabinet_related_columns:
                        print(f"✅ 可能包含机柜数据的列: {cabinet_related_columns}")
                        if len(df) > 0:
                            print(f"前3行数据:")
                            print(df[cabinet_related_columns].head(3).to_string())
                    else:
                        print(f"❌ 未发现机柜相关列")
                
                except Exception as e:
                    print(f"❌ 读取工作表 {sheet_name} 失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试PIDB文件结构失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pidb_loading_with_debug():
    """测试PIDB加载并输出调试信息"""
    print("\n=== 测试PIDB加载过程 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        # 查找PIDB文件
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx", 
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        # 创建数据加载器
        config = {}
        data_loader = DataLoader(config)
        
        print(f"开始加载PIDB文件: {pidb_file}")
        
        # 加载PIDB数据
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        print(f"\n📊 PIDB加载结果:")
        print(f"  机柜数量: {len(pidb_data.get('cabinets', []))}")
        print(f"  机架数量: {len(pidb_data.get('racks', []))}")
        
        # 检查机柜数据
        cabinets = pidb_data.get('cabinets', [])
        if cabinets:
            print(f"\n📋 加载的机柜详情:")
            for i, cabinet in enumerate(cabinets, 1):
                name = cabinet.get('name', 'Unknown')
                template = cabinet.get('template', 'Unknown')
                cabinet_type = cabinet.get('type', 'Unknown')
                source = cabinet.get('source', 'Unknown')
                print(f"  {i}. 名称: {name}")
                print(f"     模板: {template}")
                print(f"     类型: {cabinet_type}")
                print(f"     来源: {source}")
                
                # 检查是否是真实机柜名称
                if name.startswith('1105-SIS-'):
                    print(f"     ✅ 真实机柜名称")
                elif name in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']:
                    print(f"     ❌ 模板名称")
                elif name.startswith('E-'):
                    print(f"     ⚠️  默认生成的名称")
                else:
                    print(f"     ❓ 未知名称格式")
        else:
            print(f"\n❌ 未加载到任何机柜数据")
            print(f"可能的原因:")
            print(f"  1. PIDB文件中没有cabinet工作表")
            print(f"  2. cabinet工作表为空")
            print(f"  3. 列名不匹配")
            print(f"  4. 数据格式不正确")
        
        return len(cabinets) > 0
        
    except Exception as e:
        print(f"❌ 测试PIDB加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("开始调试PIDB文件结构和加载过程...")
    
    test_results = []
    
    # 执行调试
    test_results.append(debug_pidb_file_structure())
    test_results.append(test_pidb_loading_with_debug())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 调试结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("✅ 调试完成，PIDB文件结构和加载过程正常")
    else:
        print("⚠️  发现问题，需要进一步分析")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
