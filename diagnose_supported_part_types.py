#!/usr/bin/env python3
"""
诊断 supported_part_types 设置问题
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_actual_cabinet_conversion():
    """测试实际的机柜转换过程"""
    print("=== 测试实际机柜转换过程 ===")
    
    try:
        from core.allocator import IOAllocator
        from core.data_models import IOPoint, SignalType
        
        # 创建分配器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        allocator = IOAllocator(config)
        
        # 创建测试机柜字典（模拟真实数据）
        cabinet_dict = {
            'name': '1105-SIS-REL-101',
            'template': 'PPG REL',
            'type': 'Marshalling',
            'location': 'Field Area 1105',
            'rails': [
                {
                    'name': 'Rail_REL_01',
                    'position': 'Marshalling',
                    'length': 2000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'Barrier',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        }
        
        print(f"原始机柜字典:")
        print(f"  机柜名称: {cabinet_dict['name']}")
        print(f"  机柜类型: {cabinet_dict['type']}")
        print(f"  导轨配置:")
        for rail in cabinet_dict['rails']:
            print(f"    - {rail['name']}: {rail['position']}")
            for i in range(1, 11):
                part_type_key = f'PartType{i:02d}'
                if part_type_key in rail:
                    print(f"      {part_type_key}: {rail[part_type_key]}")
        
        # 执行转换
        actual_cabinet = allocator._convert_dict_to_actual_cabinet(cabinet_dict)
        
        if actual_cabinet:
            print(f"\n✅ 转换成功:")
            print(f"  机柜名称: {actual_cabinet.name}")
            print(f"  机柜类型: {actual_cabinet.cabinet_type}")
            print(f"  导轨数量: {len(actual_cabinet.rails)}")
            
            for rail in actual_cabinet.rails:
                print(f"\n  导轨: {rail.name}")
                print(f"    位置: {rail.position}")
                print(f"    长度: {rail.length}mm")
                print(f"    支持的器件类型: {rail.supported_part_types}")
                print(f"    可用长度: {rail.available_length}mm")
                
                # 测试器件兼容性
                from core.component_allocation_models import ComponentDefinition, ComponentType
                
                test_components = [
                    ComponentDefinition(
                        name="FTB1",
                        component_type=ComponentType.FIELD_TERM_IN,
                        part_number="FTB-1",
                        width=12.5
                    ),
                    ComponentDefinition(
                        name="FTB2",
                        component_type=ComponentType.FIELD_TERM_IN,
                        part_number="FTB-2",
                        width=12.5
                    ),
                    ComponentDefinition(
                        name="3000510-380C1R",
                        component_type=ComponentType.ETP,
                        part_number="3000510-380C1R",
                        width=25.0
                    )
                ]
                
                print(f"    器件兼容性测试:")
                for comp in test_components:
                    can_accommodate = rail.can_accommodate(comp)
                    comp_type_value = comp.component_type.value
                    in_supported = comp_type_value in rail.supported_part_types
                    
                    print(f"      - {comp.name} ({comp_type_value}): {'✅' if can_accommodate else '❌'}")
                    print(f"        类型匹配: {'✅' if in_supported else '❌'} ({comp_type_value} in {rail.supported_part_types})")
                    print(f"        空间足够: {'✅' if rail.available_length >= comp.space_requirement else '❌'} ({comp.space_requirement}mm 需要, {rail.available_length}mm 可用)")
        else:
            print("❌ 机柜转换失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 机柜转换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_allocation_with_real_data():
    """使用真实数据测试器件分配"""
    print("\n=== 使用真实数据测试器件分配 ===")
    
    try:
        from core.component_allocation_engine import ComponentAllocationEngine
        from core.component_allocation_models import ComponentDefinition, ComponentType, ActualCabinet, RailInstance
        from core.data_models import IOPoint, SignalType
        
        # 创建分配引擎
        engine = ComponentAllocationEngine("01C_Wiring Typical")
        
        # 创建真实的机柜（手动设置确保正确）
        rail = RailInstance(
            name="Rail_REL_01",
            position="Marshalling",
            length=2000.0,
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "Barrier"],
            reserved_from=0.0,
            reserved_to=0.0
        )
        
        cabinet = ActualCabinet(
            name="1105-SIS-REL-101",
            template_name="PPG REL",
            location="Field Area 1105",
            cabinet_type="Marshalling",
            rails=[rail]
        )
        
        # 创建测试I/O点
        io_point = IOPoint(
            tag="1105-HZ-004-3",
            signal_type=SignalType.DI,
            description="Test DI Point",
            wiring_typical="DI NIS N"
        )
        
        print(f"测试场景:")
        print(f"  I/O点: {io_point.tag}")
        print(f"  典型回路: {io_point.wiring_typical}")
        print(f"  机柜: {cabinet.name} ({cabinet.cabinet_type})")
        print(f"  导轨: {rail.name} (支持: {rail.supported_part_types})")
        
        # 执行分配
        result = engine.allocate_io_point_components(io_point, cabinet, {})
        
        print(f"\n分配结果:")
        print(f"  成功: {result.success}")
        print(f"  分配的器件数: {len(result.component_allocations)}")
        print(f"  错误数: {len(result.errors)}")
        
        if result.errors:
            print(f"  错误信息:")
            for error in result.errors:
                print(f"    - {error}")
        
        if result.component_allocations:
            print(f"  成功分配的器件:")
            for alloc in result.component_allocations:
                comp_name = alloc.component_definition.name
                comp_type = alloc.component_definition.component_type.value
                rail_name = alloc.rail_name
                print(f"    - {comp_name} ({comp_type}) -> {rail_name}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 器件分配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始 supported_part_types 设置问题诊断...")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_actual_cabinet_conversion())
    test_results.append(test_component_allocation_with_real_data())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 诊断结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过，supported_part_types 设置正常")
        print("\n🔍 如果仍有问题，可能的原因:")
        print("1. 典型回路解析器返回的器件类型不正确")
        print("2. 器件的 component_type.value 与 supported_part_types 不匹配")
        print("3. 导轨空间不足")
        print("4. 其他兼容性检查失败")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        print("\n🔧 可能的修复方向:")
        print("1. 检查 PartType 字段的提取逻辑")
        print("2. 确保 supported_part_types 列表正确设置")
        print("3. 验证器件类型枚举值与字符串的匹配")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
