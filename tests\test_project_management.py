#!/usr/bin/env python3
"""
项目管理功能测试
测试新建项目、打开项目、保存项目等功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication, QWidget, QVBoxLayout, QPushButton, QLabel, QMessageBox
from PySide6.QtCore import Qt

from utils.config_manager_simple import ConfigManager
from core.project_manager import ProjectManager
from gui.project_dialogs import NewProjectDialog, RecentProjectsDialog
from gui.styles import get_apple_style


class ProjectTestWidget(QWidget):
    """项目管理测试窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("项目管理功能测试")
        self.setFixedSize(400, 300)
        
        # 初始化项目管理器
        config_manager = ConfigManager()
        config = config_manager.load_config()
        self.project_manager = ProjectManager(config)
        
        self._setup_ui()
        self._apply_styles()
    
    def _setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        layout.setSpacing(15)
        layout.setContentsMargins(30, 30, 30, 30)
        
        # 标题
        title = QLabel("EWReborn 项目管理功能测试")
        title.setAlignment(Qt.AlignCenter)
        title.setStyleSheet("font-size: 16px; font-weight: bold; margin-bottom: 20px;")
        layout.addWidget(title)
        
        # 状态显示
        self.status_label = QLabel("当前状态: 无项目")
        self.status_label.setStyleSheet("color: #666; margin-bottom: 10px;")
        layout.addWidget(self.status_label)
        
        # 功能按钮
        self.new_project_btn = QPushButton("新建项目")
        self.new_project_btn.clicked.connect(self._test_new_project)
        layout.addWidget(self.new_project_btn)
        
        self.open_project_btn = QPushButton("打开项目")
        self.open_project_btn.clicked.connect(self._test_open_project)
        layout.addWidget(self.open_project_btn)
        
        self.save_project_btn = QPushButton("保存项目")
        self.save_project_btn.clicked.connect(self._test_save_project)
        self.save_project_btn.setEnabled(False)
        layout.addWidget(self.save_project_btn)
        
        self.recent_projects_btn = QPushButton("最近项目")
        self.recent_projects_btn.clicked.connect(self._test_recent_projects)
        layout.addWidget(self.recent_projects_btn)
        
        self.close_project_btn = QPushButton("关闭项目")
        self.close_project_btn.clicked.connect(self._test_close_project)
        self.close_project_btn.setEnabled(False)
        layout.addWidget(self.close_project_btn)
        
        layout.addStretch()
        
        # 退出按钮
        exit_btn = QPushButton("退出")
        exit_btn.clicked.connect(self.close)
        layout.addWidget(exit_btn)
    
    def _apply_styles(self):
        """应用样式"""
        self.setStyleSheet(get_apple_style())
    
    def _test_new_project(self):
        """测试新建项目"""
        try:
            dialog = NewProjectDialog(self)
            if dialog.exec() == dialog.Accepted:
                project_name, project_path, description = dialog.get_project_info()
                
                # 创建项目
                project_data = self.project_manager.create_new_project(
                    project_name, project_path, description
                )
                
                self._update_status()
                QMessageBox.information(self, "成功", f"项目 '{project_name}' 创建成功！")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"创建项目失败: {str(e)}")
    
    def _test_open_project(self):
        """测试打开项目"""
        try:
            recent_projects = self.project_manager.get_recent_projects()
            
            if recent_projects:
                dialog = RecentProjectsDialog(recent_projects, self)
                dialog.project_selected.connect(self._load_project)
                dialog.exec()
            else:
                QMessageBox.information(self, "提示", "没有最近项目，请先创建一个项目")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开项目失败: {str(e)}")
    
    def _load_project(self, file_path: str):
        """加载项目文件"""
        try:
            project_data = self.project_manager.open_project(file_path)
            self._update_status()
            
            project_name = project_data["project_info"]["name"]
            QMessageBox.information(self, "成功", f"项目 '{project_name}' 打开成功！")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"打开项目失败: {str(e)}")
    
    def _test_save_project(self):
        """测试保存项目"""
        try:
            if self.project_manager.save_project():
                self._update_status()
                QMessageBox.information(self, "成功", "项目保存成功！")
            else:
                QMessageBox.warning(self, "警告", "项目保存失败")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存项目失败: {str(e)}")
    
    def _test_recent_projects(self):
        """测试最近项目"""
        recent_projects = self.project_manager.get_recent_projects()
        
        if recent_projects:
            projects_text = "\n".join([Path(p).name for p in recent_projects])
            QMessageBox.information(self, "最近项目", f"最近项目列表:\n{projects_text}")
        else:
            QMessageBox.information(self, "最近项目", "没有最近打开的项目")
    
    def _test_close_project(self):
        """测试关闭项目"""
        try:
            if self.project_manager.close_project():
                self._update_status()
                QMessageBox.information(self, "成功", "项目已关闭")
        
        except Exception as e:
            QMessageBox.critical(self, "错误", f"关闭项目失败: {str(e)}")
    
    def _update_status(self):
        """更新状态显示"""
        if self.project_manager.has_current_project:
            project_name = self.project_manager.current_project_name
            modified = " (已修改)" if self.project_manager.is_modified else ""
            self.status_label.setText(f"当前项目: {project_name}{modified}")
            
            self.save_project_btn.setEnabled(True)
            self.close_project_btn.setEnabled(True)
        else:
            self.status_label.setText("当前状态: 无项目")
            self.save_project_btn.setEnabled(False)
            self.close_project_btn.setEnabled(False)


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 创建测试窗口
    test_widget = ProjectTestWidget()
    test_widget.show()
    
    # 居中显示
    screen = app.primaryScreen().availableGeometry()
    x = (screen.width() - test_widget.width()) // 2
    y = (screen.height() - test_widget.height()) // 2
    test_widget.move(x, y)
    
    return app.exec()


if __name__ == "__main__":
    sys.exit(main())
