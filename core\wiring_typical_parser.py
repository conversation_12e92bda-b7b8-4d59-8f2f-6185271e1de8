"""
典型回路XML解析器
从典型回路XML文件中提取器件清单和配置信息
"""

import xml.etree.ElementTree as ET
from typing import Dict, List, Optional, Any
from pathlib import Path

from core.logger import get_logger
from core.component_allocation_models import (
    ComponentDefinition, ComponentType, MountingType, 
    WiringTypicalDefinition
)


class WiringTypicalParser:
    """典型回路XML解析器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def parse_wiring_typical_file(self, file_path: str) -> WiringTypicalDefinition:
        """
        解析单个典型回路XML文件
        
        Args:
            file_path: XML文件路径
            
        Returns:
            典型回路定义对象
        """
        try:
            tree = ET.parse(file_path)
            root = tree.getroot()
            
            # 获取典型回路基本信息
            typical_name = root.get('Name', Path(file_path).stem)
            
            # 解析ProfileProperties获取信号类型和本安信息
            signal_type = self._extract_signal_type(root)
            intrinsic = self._extract_intrinsic_type(root)
            
            # 提取机柜类型要求
            required_cabinet_type = self._extract_required_cabinet_type(root)

            # 解析MarshallingCabinet下的器件
            marshalling_components = self._extract_marshalling_components(root)

            # 解析SystemCabinet下的器件（如果存在）
            system_components = self._extract_system_components(root)
            
            # 解析电缆和导线信息
            cables = self._extract_cables(root)
            wires = self._extract_wires(root)
            
            typical_definition = WiringTypicalDefinition(
                name=typical_name,
                signal_type=signal_type,
                intrinsic=intrinsic,
                required_cabinet_type=required_cabinet_type,
                marshalling_components=marshalling_components,
                system_components=system_components,
                cables=cables,
                wires=wires
            )
            
            self.logger.debug(f"解析典型回路 {typical_name}: {len(marshalling_components)}个汇控柜器件")
            return typical_definition
            
        except Exception as e:
            self.logger.error(f"解析典型回路文件失败 {file_path}: {e}")
            raise
    
    def _extract_signal_type(self, root: ET.Element) -> str:
        """提取信号类型"""
        # 从ProfileProperties中查找SignalType
        for prop in root.findall(".//ProfileProperty[@Name='SignalType']"):
            return prop.get('Value', 'Unknown')
        
        # 从ProfileSignatures中查找
        for sig in root.findall(".//ProfileSignature[@Name='Intrinsic']"):
            return sig.get('Value', 'Unknown')
        
        return 'Unknown'
    
    def _extract_intrinsic_type(self, root: ET.Element) -> str:
        """提取本安类型"""
        # 从ProfileSignatures中查找Intrinsic
        for sig in root.findall(".//ProfileSignature[@Name='Intrinsic']"):
            return sig.get('Value', 'NIS')
        
        return 'NIS'

    def _extract_required_cabinet_type(self, root: ET.Element) -> str:
        """提取典型回路要求的机柜类型"""
        # 查找MarshallingCabinet或System-MarshallingCabinet节点中的CabinetType

        # 首先查找System-MarshallingCabinet节点
        system_marshalling_cabinet = root.find(".//ProfileComponent[@Name='System-MarshallingCabinet']")
        if system_marshalling_cabinet is not None:
            cabinet_type_prop = system_marshalling_cabinet.find(".//ProfileProperty[@Name='CabinetType']")
            if cabinet_type_prop is not None:
                cabinet_type = cabinet_type_prop.get('Value', '')
                if cabinet_type:
                    self.logger.debug(f"从System-MarshallingCabinet节点提取机柜类型: {cabinet_type}")
                    return cabinet_type

        # 查找标准的MarshallingCabinet节点
        marshalling_cabinet = root.find(".//ProfileComponent[@Name='MarshallingCabinet']")
        if marshalling_cabinet is not None:
            cabinet_type_prop = marshalling_cabinet.find(".//ProfileProperty[@Name='CabinetType']")
            if cabinet_type_prop is not None:
                cabinet_type = cabinet_type_prop.get('Value', '')
                if cabinet_type:
                    self.logger.debug(f"从MarshallingCabinet节点提取机柜类型: {cabinet_type}")
                    return cabinet_type

        # 查找SystemCabinet节点
        system_cabinet = root.find(".//ProfileComponent[@Name='SystemCabinet']")
        if system_cabinet is not None:
            cabinet_type_prop = system_cabinet.find(".//ProfileProperty[@Name='CabinetType']")
            if cabinet_type_prop is not None:
                cabinet_type = cabinet_type_prop.get('Value', '')
                if cabinet_type:
                    self.logger.debug(f"从SystemCabinet节点提取机柜类型: {cabinet_type}")
                    return cabinet_type

        # 默认返回Marshalling
        root_name = root.get('Name', 'Unknown')
        self.logger.debug(f"典型回路 '{root_name}' 未找到明确的机柜类型定义，使用默认值: Marshalling")
        return 'Marshalling'

    def _extract_marshalling_components(self, root: ET.Element) -> List[ComponentDefinition]:
        """提取MarshallingCabinet或System-MarshallingCabinet下的器件清单"""
        components = []

        # 首先查找标准的MarshallingCabinet节点
        marshalling_cabinet = root.find(".//ProfileComponent[@Name='MarshallingCabinet']")
        cabinet_type = "MarshallingCabinet"

        # 如果没有找到标准节点，查找System-MarshallingCabinet节点
        if marshalling_cabinet is None:
            marshalling_cabinet = root.find(".//ProfileComponent[@Name='System-MarshallingCabinet']")
            cabinet_type = "System-MarshallingCabinet"

        # 如果两种节点都没有找到，记录警告
        if marshalling_cabinet is None:
            root_name = root.get('Name', 'Unknown')
            self.logger.warning(f"典型回路 '{root_name}' 中未找到MarshallingCabinet或System-MarshallingCabinet节点")

            # 调试信息：列出所有可用的ProfileComponent
            all_components = root.findall(".//ProfileComponent")
            component_names = [comp.get('Name', 'Unnamed') for comp in all_components[:5]]  # 只显示前5个
            self.logger.debug(f"可用的ProfileComponent: {component_names}")
            return components

        # 记录找到的机柜类型
        root_name = root.get('Name', 'Unknown')
        self.logger.debug(f"典型回路 '{root_name}' 使用 {cabinet_type} 节点")
        
        # 遍历MarshallingCabinet下的直接子器件
        for component_elem in marshalling_cabinet.findall("./Components/ProfileComponent"):
            component_def = self._parse_component_element(component_elem, cabinet_type)
            if component_def:
                components.append(component_def)
        
        return components
    
    def _extract_system_components(self, root: ET.Element) -> List[ComponentDefinition]:
        """提取SystemCabinet下的器件清单"""
        components = []
        
        # 查找SystemCabinet节点
        system_cabinet = root.find(".//ProfileComponent[@Name='SystemCabinet']")
        if system_cabinet is None:
            return components
        
        # 遍历SystemCabinet下的器件
        for component_elem in system_cabinet.findall(".//ProfileComponent[@Type='HardwarePart']"):
            component_def = self._parse_component_element(component_elem, "SystemCabinet")
            if component_def:
                components.append(component_def)
        
        return components
    
    def _parse_component_element(self, element: ET.Element, cabinet_location: str = "MarshallingCabinet") -> Optional[ComponentDefinition]:
        """解析单个器件元素"""
        try:
            name = element.get('Name', '')
            count = int(element.get('Count', 1))

            # 过滤掉不需要分配的组件
            if self._should_skip_component(name, element):
                self.logger.debug(f"跳过不需要分配的组件: {name}")
                return None

            # 提取器件属性
            properties = self._extract_properties(element)

            # 确定器件类型
            hardware_type = properties.get('HardwareType', '')
            component_type = self._map_hardware_type_to_component_type(hardware_type, name)

            # 提取尺寸信息
            width = self._parse_float_property(properties.get('Width'))
            length = self._parse_float_property(properties.get('Length'))
            height = self._parse_float_property(properties.get('Height'))

            # 提取安装方式
            mounting_type_str = properties.get('MountingType', 'Rail')
            mounting_type = MountingType.RAIL if mounting_type_str == 'Rail' else MountingType.RACK

            # 提取通道信息
            channel_count = self._parse_int_property(properties.get('ChannelCount'))

            # 提取槽位信息
            slots = self._extract_slots(element)

            component_def = ComponentDefinition(
                name=name,
                component_type=component_type,
                part_number=properties.get('PartNumber', ''),
                count=count,
                width=width,
                length=length,
                height=height,
                mounting_type=mounting_type,
                channel_count=channel_count,
                slots=slots,
                cabinet_location=cabinet_location,
                properties=properties
            )

            return component_def

        except Exception as e:
            self.logger.warning(f"解析器件元素失败: {e}")
            return None
    
    def _extract_properties(self, element: ET.Element) -> Dict[str, str]:
        """提取器件的所有属性"""
        properties = {}
        
        for prop in element.findall(".//ProfileProperty"):
            name = prop.get('Name', '')
            value = prop.get('Value', '')
            if name and value:
                properties[name] = value
        
        return properties
    
    def _should_skip_component(self, name: str, element: ET.Element) -> bool:
        """判断是否应该跳过该组件（不需要分配）"""
        # 跳过 Wires 组件
        if name.lower() in ['wires', 'wire']:
            return True

        # 跳过 Type="Wire" 的组件
        element_type = element.get('Type', '')
        if element_type.lower() == 'wire':
            return True

        # 跳过没有 HardwareType 属性的组件（通常是连接线等）
        properties = self._extract_properties(element)
        hardware_type = properties.get('HardwareType', '')
        if not hardware_type:
            return True

        return False

    def _map_hardware_type_to_component_type(self, hardware_type: str, component_name: str = '') -> ComponentType:
        """将HardwareType映射到ComponentType"""
        type_mapping = {
            'FieldTermIn': ComponentType.FIELD_TERM_IN,
            'FieldTermOut': ComponentType.FIELD_TERM_OUT,
            'ETP': ComponentType.ETP,
            'Barrier': ComponentType.BARRIER,
            'IOModule': ComponentType.IO_MODULE,
            'Chassis': ComponentType.CHASSIS,
            'Mainprocessorchassis': ComponentType.CHASSIS,
            'Rxmchassis': ComponentType.CHASSIS,
            'MainProcessorChassis': ComponentType.CHASSIS,
            'RxmChassis': ComponentType.CHASSIS
        }

        # 根据组件名称进行特殊判断
        if component_name:
            name_lower = component_name.lower()
            # FTB类器件根据名称判断输入/输出类型
            if name_lower.startswith('ftb'):
                # 如果HardwareType已经明确指定，优先使用HardwareType
                if hardware_type in ['FieldTermIn', 'FieldTermOut']:
                    return type_mapping.get(hardware_type, ComponentType.FIELD_TERM_IN)
                # 否则根据名称推断（这里可以根据实际规则调整）
                return ComponentType.FIELD_TERM_IN
            # 包含chassis的通常是机架
            elif 'chassis' in name_lower:
                return ComponentType.CHASSIS

        return type_mapping.get(hardware_type, ComponentType.ETP)
    
    def _extract_slots(self, element: ET.Element) -> List[str]:
        """提取槽位信息"""
        slots = []
        
        # 查找Slots节点下的Names属性
        slots_elem = element.find(".//ProfileComponent[@Type='Slots']")
        if slots_elem is not None:
            for prop in slots_elem.findall(".//ProfileProperty[@Name='Names']"):
                names_value = prop.get('Value', '')
                if names_value:
                    # 解析逗号分隔的槽位名称
                    slots = [name.strip() for name in names_value.split(',') if name.strip()]
                    break
        
        return slots
    
    def _extract_cables(self, root: ET.Element) -> List[Dict[str, Any]]:
        """提取电缆信息"""
        cables = []
        
        cables_elem = root.find(".//ProfileComponent[@Name='Cables']")
        if cables_elem is not None:
            for cable_elem in cables_elem.findall(".//ProfileComponent[@Type='Cable']"):
                cable_info = {
                    'name': cable_elem.get('Name', ''),
                    'properties': self._extract_properties(cable_elem)
                }
                cables.append(cable_info)
        
        return cables
    
    def _extract_wires(self, root: ET.Element) -> List[Dict[str, Any]]:
        """提取导线信息"""
        wires = []

        wires_elem = root.find(".//ProfileComponent[@Name='Wires']")
        if wires_elem is not None:
            for wire_elem in wires_elem.findall(".//ProfileComponent[@Type='Wire']"):
                properties = self._extract_properties(wire_elem)

                # 提取End1和End2信息
                end1 = properties.get('End1', '')
                end2 = properties.get('End2', '')

                # 从End1和End2提取ScrewID（最后一个.之后的内容）
                end1_screw_id = self._extract_screw_id(end1)
                end2_screw_id = self._extract_screw_id(end2)

                wire_info = {
                    'name': wire_elem.get('Name', ''),
                    'end1': end1,
                    'end2': end2,
                    'end1_screw_id': end1_screw_id,
                    'end2_screw_id': end2_screw_id,
                    'end1_naming_convention': properties.get('End1_NamingConvention', ''),
                    'end2_naming_convention': properties.get('End2_NamingConvention', ''),
                    'color': properties.get('Color', ''),
                    'wire_type': properties.get('WireType', ''),
                    'insulation': properties.get('Insulation', ''),
                    'properties': properties
                }
                wires.append(wire_info)

        return wires

    def _extract_screw_id(self, end_path: str) -> str:
        """从End路径中提取ScrewID（最后一个.之后的内容）"""
        if not end_path:
            return ''

        # 找到最后一个.的位置
        last_dot_index = end_path.rfind('.')
        if last_dot_index != -1:
            return end_path[last_dot_index + 1:]
        else:
            return end_path
    
    def _parse_float_property(self, value: Optional[str]) -> Optional[float]:
        """解析浮点数属性"""
        if not value or value == '':
            return None
        try:
            return float(value)
        except (ValueError, TypeError):
            return None
    
    def _parse_int_property(self, value: Optional[str]) -> Optional[int]:
        """解析整数属性"""
        if not value or value == '':
            return None
        try:
            return int(value)
        except (ValueError, TypeError):
            return None


class WiringTypicalManager:
    """典型回路管理器"""
    
    def __init__(self, typical_directory: str):
        self.typical_directory = Path(typical_directory)
        self.parser = WiringTypicalParser()
        self.logger = get_logger(__name__)
        self._typicals_cache: Dict[str, WiringTypicalDefinition] = {}
    
    def load_all_typicals(self) -> Dict[str, WiringTypicalDefinition]:
        """加载所有典型回路"""
        if self._typicals_cache:
            return self._typicals_cache
        
        typicals = {}
        
        if not self.typical_directory.exists():
            self.logger.error(f"典型回路目录不存在: {self.typical_directory}")
            return typicals
        
        # 遍历所有XML文件
        xml_files = list(self.typical_directory.glob("*.xml"))
        self.logger.info(f"开始加载典型回路，找到 {len(xml_files)} 个XML文件")

        for xml_file in xml_files:
            try:
                self.logger.debug(f"正在解析文件: {xml_file.name}")
                typical_def = self.parser.parse_wiring_typical_file(str(xml_file))
                typicals[typical_def.name] = typical_def
                self.logger.debug(f"成功加载典型回路: {typical_def.name} (器件数: {len(typical_def.marshalling_components)})")
            except Exception as e:
                self.logger.error(f"加载典型回路文件失败 {xml_file.name}: {e}")
                # 继续处理其他文件，不因单个文件失败而中断

        self._typicals_cache = typicals
        self.logger.info(f"典型回路加载完成: 成功 {len(typicals)} 个，总计 {len(xml_files)} 个文件")
        return typicals
    
    def get_typical_by_name(self, name: str) -> Optional[WiringTypicalDefinition]:
        """根据名称获取典型回路"""
        if not self._typicals_cache:
            self.load_all_typicals()
        
        return self._typicals_cache.get(name)
    
    def get_marshalling_components_for_io_point(self, io_point) -> List[ComponentDefinition]:
        """获取I/O点需要的汇控柜器件清单"""
        typical_name = getattr(io_point, 'wiring_typical', None)
        if not typical_name:
            self.logger.warning(f"I/O点 {getattr(io_point, 'tag', 'Unknown')} 没有典型回路信息")
            return []
        
        typical_def = self.get_typical_by_name(typical_name)
        if not typical_def:
            self.logger.warning(f"未找到典型回路: {typical_name}")
            return []
        
        return typical_def.get_marshalling_components()
