#!/usr/bin/env python3
"""
调试I/O点分配问题的专用脚本
重点检查典型机柜和典型回路的读取功能
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_data_loading():
    """调试数据加载过程"""
    print("=== 调试I/O点分配系统数据加载 ===")
    
    try:
        # 1. 测试配置加载
        print("\n1. 测试配置管理...")
        from utils.config_manager_simple import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print(f"✓ 配置加载成功")
        print(f"  - 机柜配置路径: {config_manager.get('data_paths.cabinet_profiles')}")
        print(f"  - 典型回路路径: {config_manager.get('data_paths.wiring_typical')}")
        print(f"  - IODB路径: {config_manager.get('data_paths.iodb')}")
        print(f"  - PIDB路径: {config_manager.get('data_paths.pidb')}")
        
        # 2. 检查文件路径是否存在
        print("\n2. 检查数据文件路径...")
        cabinet_path = Path(config_manager.get('data_paths.cabinet_profiles', ''))
        wiring_path = Path(config_manager.get('data_paths.wiring_typical', ''))
        iodb_path = Path(config_manager.get('data_paths.iodb', ''))
        pidb_path = Path(config_manager.get('data_paths.pidb', ''))
        
        print(f"  - 机柜配置目录存在: {cabinet_path.exists()} ({cabinet_path})")
        print(f"  - 典型回路目录存在: {wiring_path.exists()} ({wiring_path})")
        print(f"  - IODB目录存在: {iodb_path.exists()} ({iodb_path})")
        print(f"  - PIDB目录存在: {pidb_path.exists()} ({pidb_path})")
        
        if cabinet_path.exists():
            cabinet_files = list(cabinet_path.glob("*.xml"))
            print(f"    机柜配置文件数量: {len(cabinet_files)}")
            for f in cabinet_files[:3]:  # 显示前3个
                print(f"      - {f.name}")
        
        if wiring_path.exists():
            wiring_files = list(wiring_path.glob("*.xml"))
            print(f"    典型回路文件数量: {len(wiring_files)}")
            for f in wiring_files[:3]:  # 显示前3个
                print(f"      - {f.name}")
        
        if iodb_path.exists():
            iodb_files = list(iodb_path.glob("*.xlsx"))
            print(f"    IODB文件数量: {len(iodb_files)}")
            for f in iodb_files:
                print(f"      - {f.name}")
        
        # 3. 测试数据加载器
        print("\n3. 测试数据加载器...")
        from core.data_loader_simple import DataLoader
        data_loader = DataLoader(config)
        
        # 测试机柜配置加载
        print("  3.1 加载机柜配置...")
        cabinet_profiles = data_loader.load_cabinet_profiles()
        print(f"    ✓ 加载了 {len(cabinet_profiles)} 个机柜配置")
        
        for name, profile in list(cabinet_profiles.items())[:2]:  # 显示前2个
            rails_count = len(profile.get('rails', []))
            racks_count = len(profile.get('racks', []))
            print(f"      - {name}: {rails_count}个导轨, {racks_count}个机架")
        
        # 测试典型回路加载
        print("  3.2 加载典型回路...")
        wiring_typicals = data_loader.load_wiring_typicals()
        print(f"    ✓ 加载了 {len(wiring_typicals)} 个典型回路")
        
        for name, typical in list(wiring_typicals.items())[:2]:  # 显示前2个
            components_count = len(typical.get('components', []))
            connections_count = len(typical.get('connections', []))
            print(f"      - {name}: {components_count}个组件, {connections_count}个连接")
        
        # 测试IODB数据加载
        print("  3.3 加载IODB数据...")
        if iodb_files:
            iodb_file = iodb_files[0]  # 使用第一个IODB文件
            iodb_data = data_loader.load_iodb_data(str(iodb_file))
            io_points = iodb_data.get('io_points', [])
            cables = iodb_data.get('cables', {})
            print(f"    ✓ 加载了 {len(io_points)} 个I/O点, {len(cables)} 条电缆")
            
            # 显示前几个I/O点的信息
            for i, point in enumerate(io_points[:3]):
                print(f"      - I/O点{i+1}: {point.tag}, 电缆: {point.cable_name}, 信号: {point.signal_type}")
            
            # 显示前几条电缆的信息
            for name, cable in list(cables.items())[:3]:
                io_count = len(cable.get('io_points', []))
                print(f"      - 电缆: {name}, I/O点数: {io_count}")
        else:
            print("    ⚠️ 未找到IODB文件")
        
        # 4. 测试数据模型创建
        print("\n4. 测试数据模型创建...")
        if iodb_files:
            models = data_loader.create_data_models(
                iodb_data,
                {},  # 暂时不加载PIDB
                cabinet_profiles,
                wiring_typicals
            )
            
            print(f"    ✓ 创建数据模型成功:")
            print(f"      - I/O点: {len(models['io_points'])}")
            print(f"      - 电缆: {len(models['cables'])}")
            print(f"      - 机柜: {len(models['cabinets'])}")
            print(f"      - 典型回路: {len(models['wiring_typicals'])}")
            
            # 检查机柜对象的结构
            print("    机柜对象详情:")
            for cabinet in models['cabinets'][:2]:  # 显示前2个机柜
                rails_count = len(cabinet.get('rails', []))
                print(f"      - {cabinet['name']}: {rails_count}个导轨")
                if rails_count == 0:
                    print(f"        ⚠️ 警告: 机柜 {cabinet['name']} 没有导轨!")
            
            # 检查电缆对象的结构
            print("    电缆对象详情:")
            for cable in models['cables'][:3]:  # 显示前3条电缆
                io_count = len(cable.get('io_points', []))
                print(f"      - {cable['name']}: {io_count}个I/O点")
                if io_count == 0:
                    print(f"        ⚠️ 警告: 电缆 {cable['name']} 没有I/O点!")
        
        # 5. 测试分配算法
        print("\n5. 测试I/O分配算法...")
        if iodb_files and models:
            from core.allocator import IOAllocator
            allocator = IOAllocator(config)
            
            print("    执行分配...")
            allocation_result = allocator.allocate_io_points(
                models['cables'],
                models['cabinets'],
                {wt.name: {'components': []} for wt in models['wiring_typicals']}
            )
            
            print(f"    ✓ 分配完成:")
            print(f"      - 分配成功: {len(allocation_result.allocated_points)}")
            print(f"      - 分配失败: {len(allocation_result.failed_points)}")
            print(f"      - 错误数量: {len(allocation_result.errors)}")
            print(f"      - 警告数量: {len(allocation_result.warnings)}")
            
            if allocation_result.summary:
                summary = allocation_result.summary
                print(f"      - 总点数: {summary.get('total_points', 0)}")
                print(f"      - 成功率: {summary.get('success_rate', 0):.1f}%")
            
            # 显示错误信息
            if allocation_result.errors:
                print("    错误信息:")
                for error in allocation_result.errors[:3]:  # 显示前3个错误
                    print(f"      - {error}")
            
            # 显示警告信息
            if allocation_result.warnings:
                print("    警告信息:")
                for warning in allocation_result.warnings[:3]:  # 显示前3个警告
                    print(f"      - {warning}")
        
        print("\n=== 调试完成 ===")
        return True
        
    except Exception as e:
        print(f"\n✗ 调试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = debug_data_loading()
    if success:
        print("\n🎉 调试成功完成!")
    else:
        print("\n❌ 调试过程中发现问题，请检查错误信息。")
