# EWReborn 测试套件

这个文件夹包含了EWReborn I/O点分配系统的所有测试脚本、演示脚本和调试工具。

## 📁 文件结构

### 🧪 核心测试脚本
- `test_modules.py` - 模块导入和基本功能测试
- `test_complete_system.py` - 完整系统集成测试
- `test_final_integration.py` - 最终集成验证
- `test_basic_functionality.py` - 基础功能测试
- `test_simple.py` - 简单快速测试

### 🎯 功能专项测试
- `test_gui.py` - GUI组件测试
- `test_gui_naming_config.py` - GUI命名配置测试
- `test_material_theme.py` - Material主题测试
- `test_app_startup.py` - 应用启动测试
- `test_logo.py` - Logo显示测试

### 🔧 特定功能测试
- `test_enhanced_functionality.py` - 增强功能测试
- `test_spare_functionality.py` - Spare点功能测试
- `test_spare_integration.py` - Spare点集成测试
- `test_complete_spare_system.py` - 完整Spare系统测试
- `test_project_management.py` - 项目管理测试
- `test_project_mgmt.py` - 项目管理器测试

### 🛠️ 修复验证测试
- `test_cable_validation_fix.py` - 电缆验证修复测试
- `test_cable_validation_issue.py` - 电缆验证问题测试
- `test_data_loading_fix.py` - 数据加载修复测试

### 🎨 界面和配置测试
- `test_file_dialog_defaults.py` - 文件对话框默认路径测试
- `test_default_prefixes.py` - 默认前缀测试
- `test_flexible_naming.py` - 灵活命名测试
- `test_editable_fixed_text.py` - 可编辑固定文本测试
- `test_simple_dialog.py` - 简单对话框测试

### 📊 报表和I/O测试
- `test_io_report_generation.py` - I/O报表生成测试
- `test_import.py` - 导入功能测试

### 🎭 演示脚本
- `demo_file_dialog_defaults.py` - 文件对话框默认路径演示
- `demo_naming_config.py` - 命名配置演示
- `demo_default_prefix_naming.py` - 默认前缀命名演示

### 🐛 调试工具
- `debug_naming_dialog.py` - 命名对话框调试工具

## 🚀 快速开始

### 运行基础测试
```bash
# 快速验证系统基本功能
python tests/test_simple.py

# 完整模块测试
python tests/test_modules.py

# 完整系统测试
python tests/test_complete_system.py
```

### 运行GUI测试
```bash
# GUI组件测试
python tests/test_gui.py

# Material主题测试
python tests/test_material_theme.py

# 应用启动测试
python tests/test_app_startup.py
```

### 查看功能演示
```bash
# 文件对话框功能演示
python tests/demo_file_dialog_defaults.py

# 命名配置演示
python tests/demo_naming_config.py
```

## 📋 测试要求

### 环境要求
- Python 3.8+
- 已安装项目依赖：`pip install -r requirements.txt`
- 确保项目根目录在Python路径中

### 数据要求
- 测试数据文件位于 `data/` 文件夹
- IODB和PIDB示例文件
- XML配置文件

## 🔍 故障排除

### 常见问题
1. **模块导入错误**：确保从项目根目录运行测试
2. **数据文件缺失**：检查 `data/` 文件夹是否包含必要的测试数据
3. **GUI测试失败**：确保系统支持图形界面显示

### 调试建议
- 使用 `debug_naming_dialog.py` 调试命名相关问题
- 查看 `logs/ewreborn.log` 获取详细错误信息
- 运行 `test_simple.py` 快速验证基本环境

## 📝 贡献指南

### 添加新测试
1. 在 `tests/` 文件夹中创建新的测试文件
2. 遵循命名约定：`test_功能名称.py`
3. 包含适当的文档字符串和注释
4. 更新此README文件

### 测试最佳实践
- 每个测试应该独立运行
- 包含清晰的成功/失败指示
- 提供有意义的错误消息
- 清理测试产生的临时文件

---

**注意**：这些测试脚本已从项目根目录整理到此文件夹，以保持项目结构的整洁性。
