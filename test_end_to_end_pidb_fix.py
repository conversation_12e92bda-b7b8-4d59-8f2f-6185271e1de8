#!/usr/bin/env python3
"""
端到端PIDB修复测试
验证从PIDB加载到分配器使用的完整数据流
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_end_to_end_pidb_fix():
    """测试端到端PIDB修复"""
    print("=== 端到端PIDB修复测试 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        from core.data_models import IOPoint, SignalType
        
        # 步骤1: 加载真实PIDB文件
        print("步骤1: 加载真实PIDB文件")
        
        # 查找PIDB文件
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("❌ 未找到PIDB文件")
            return False
        
        print(f"✅ 找到PIDB文件: {pidb_file}")
        
        # 创建数据加载器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        
        data_loader = DataLoader(config)
        
        # 加载PIDB数据
        pidb_data = data_loader.load_pidb_data(pidb_file)
        print(f"PIDB数据加载完成: {len(pidb_data.get('cabinets', []))} 个机柜")
        
        # 步骤2: 创建模拟的IODB数据和机柜配置
        print("\n步骤2: 创建模拟数据")
        
        # 模拟IODB数据
        iodb_data = {
            'io_points': [
                IOPoint(
                    tag="1105-RST-10001",
                    signal_type=SignalType.DI,
                    description="Reset Switch",
                    wiring_typical="DI NIS REMOTE",
                    location="Field Area 1105"
                )
            ],
            'cables': {
                'Cable_1105_RST': {
                    'name': 'Cable_1105_RST',
                    'io_points': [
                        IOPoint(
                            tag="1105-RST-10001",
                            signal_type=SignalType.DI,
                            description="Reset Switch",
                            wiring_typical="DI NIS REMOTE",
                            location="Field Area 1105"
                        )
                    ]
                }
            }
        }
        
        # 模拟机柜配置文件
        cabinet_profiles = {
            'PPG BAR': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_MAR_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG SYS': {
                'type': 'System',
                'rails': [
                    {
                        'name': 'Rail_SYS_01',
                        'position': 'System',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'ETP',
                        'PartType02': 'IOModule',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG REL': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_REL_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG RIO': {
                'type': 'System/Marshalling',
                'rails': [
                    {
                        'name': 'Rail_RIO_01',
                        'position': 'Mixed',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'IOModule',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            }
        }
        
        wiring_typicals = {}
        
        # 步骤3: 创建数据模型
        print("\n步骤3: 创建数据模型")
        
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            wiring_typicals
        )
        
        cabinets = models.get('cabinets', [])
        cables = models.get('cables', [])
        
        print(f"数据模型创建完成:")
        print(f"  机柜数量: {len(cabinets)}")
        print(f"  电缆数量: {len(cables)}")
        
        # 检查机柜名称
        print(f"\n机柜名称检查:")
        real_name_count = 0
        for cabinet in cabinets:
            name = cabinet.get('name', 'Unknown')
            template = cabinet.get('template', 'Unknown')
            source = cabinet.get('source', 'Unknown')
            print(f"  - {name} (模板: {template}, 来源: {source})")
            
            if name.startswith('1105-SIS-'):
                print(f"    ✅ 真实机柜名称")
                real_name_count += 1
            elif name in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']:
                print(f"    ❌ 模板名称")
            else:
                print(f"    ⚠️  其他名称格式")
        
        # 步骤4: 执行分配
        print(f"\n步骤4: 执行分配")
        
        allocator = IOAllocator(config)
        
        # 执行分配
        result = allocator.allocate_io_points(cables, cabinets, wiring_typicals)
        
        print(f"分配结果:")
        print(f"  分配成功: {result.success}")
        print(f"  成功分配: {len(result.allocated_points)}")
        print(f"  失败分配: {len(result.failed_points)}")
        
        # 步骤5: 检查分配结果中的机柜名称
        print(f"\n步骤5: 检查分配结果")
        
        allocation_uses_real_names = False
        if result.allocated_points:
            for io_point in result.allocated_points:
                cabinet_name = getattr(io_point, 'cabinet', 'Unknown')
                print(f"  - {io_point.tag} 分配到机柜: {cabinet_name}")
                
                if cabinet_name.startswith('1105-SIS-'):
                    print(f"    ✅ 分配结果使用真实机柜名称")
                    allocation_uses_real_names = True
                elif cabinet_name in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']:
                    print(f"    ❌ 分配结果仍使用模板名称")
                else:
                    print(f"    ⚠️  分配结果使用其他名称格式: {cabinet_name}")
        
        # 检查错误和警告信息
        template_warning_found = False
        real_name_in_logs = False
        
        if result.errors:
            print(f"\n错误信息检查:")
            for error in result.errors:
                print(f"  - {error}")
                if any(template in error for template in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']):
                    print(f"    ❌ 错误信息包含模板名称")
                    template_warning_found = True
                elif any(real_name in error for real_name in ['1105-SIS-SYS-101', '1105-SIS-BAR-101', '1105-SIS-REL-101', '1105-SIS-RIO-001']):
                    print(f"    ✅ 错误信息包含真实机柜名称")
                    real_name_in_logs = True
        
        if result.warnings:
            print(f"\n警告信息检查:")
            for warning in result.warnings:
                print(f"  - {warning}")
                if any(template in warning for template in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']):
                    print(f"    ❌ 警告信息包含模板名称")
                    template_warning_found = True
                elif any(real_name in warning for real_name in ['1105-SIS-SYS-101', '1105-SIS-BAR-101', '1105-SIS-REL-101', '1105-SIS-RIO-001']):
                    print(f"    ✅ 警告信息包含真实机柜名称")
                    real_name_in_logs = True
        
        # 步骤6: 评估修复效果
        print(f"\n步骤6: 修复效果评估")
        
        success_criteria = [
            (real_name_count > 0, "PIDB数据正确加载"),
            (real_name_count == len(cabinets), "所有机柜使用真实名称"),
            (not template_warning_found, "日志不包含模板名称警告"),
            (allocation_uses_real_names or real_name_in_logs, "分配过程使用真实名称")
        ]
        
        passed_criteria = 0
        for passed, description in success_criteria:
            status = "✅" if passed else "❌"
            print(f"  {status} {description}")
            if passed:
                passed_criteria += 1
        
        print(f"\n修复成功率: {passed_criteria}/{len(success_criteria)} ({passed_criteria/len(success_criteria)*100:.1f}%)")
        
        return passed_criteria == len(success_criteria)
        
    except Exception as e:
        print(f"❌ 端到端测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始端到端PIDB修复测试...")
    
    success = test_end_to_end_pidb_fix()
    
    print(f"\n=== 最终测试结果 ===")
    
    if success:
        print("🎉 端到端PIDB修复测试完全成功！")
        print("\n🔧 修复完成总结：")
        print("✅ 1. PIDB cabinet工作表正确读取")
        print("✅ 2. 真实机柜名称正确提取")
        print("✅ 3. 数据模型创建使用真实名称")
        print("✅ 4. 分配器接收真实机柜名称")
        print("✅ 5. 日志显示真实机柜名称")
        
        print("\n📋 修复前后对比：")
        print("❌ 修复前:")
        print("   - 系统显示: '检测到机柜名称是模板名称: PPG BAR'")
        print("   - 分配日志: '目标机柜: PPG BAR'")
        print("✅ 修复后:")
        print("   - 系统显示: '使用PIDB数据中的机柜'")
        print("   - 分配日志: '目标机柜: 1105-SIS-BAR-101'")
        
        return True
    else:
        print("⚠️  端到端测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
