"""
机柜字典转换测试脚本
验证机柜字典到ActualCabinet对象的转换是否正常工作
"""

import logging
from core.logger import setup_logging
from core.allocator import IOAllocator
from core.component_allocation_models import ActualCabinet, RailInstance

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_cabinet_conversion.log', mode='w', encoding='utf-8')
        ]
    )

def create_test_cabinet_dict():
    """创建测试用的机柜字典"""
    return {
        'name': 'E-1001',
        'template': 'PPG BAR',
        'location': 'Area 1',
        'type': 'Marshalling',
        'rails': [
            {
                'name': 'Rail_A',
                'position': 'Top',
                'length': 1000.0,
                'io_type': 'Analog',
                'intrinsic': 'IS',
                'voltage_level': 24,
                'PartType01': 'FieldTermIn',
                'PartType02': 'FieldTermOut',
                'reserved_from': 0.0,
                'reserved_to': 50.0
            },
            {
                'name': 'Rail_B',
                'position': 'Middle',
                'length': 800.0,
                'io_type': 'Digital',
                'intrinsic': 'NIS',
                'voltage_level': 24,
                'PartType01': 'ETP',
                'PartType02': 'Barrier',
                'reserved_from': 0.0,
                'reserved_to': 0.0
            }
        ]
    }

def test_cabinet_conversion():
    """测试机柜字典转换功能"""
    print("=== 机柜字典转换测试 ===")
    
    # 设置测试日志
    setup_test_logging()
    
    # 创建分配器实例
    config = {
        'allocation_settings': {
            'enable_parttype_matching': True,
            'enable_detailed_logging': True
        }
    }
    
    try:
        allocator = IOAllocator(config)
        print("✅ 分配器创建成功")
        
        # 创建测试机柜字典
        cabinet_dict = create_test_cabinet_dict()
        print(f"📋 测试机柜字典: {cabinet_dict['name']}")
        print(f"   - 模板: {cabinet_dict['template']}")
        print(f"   - 导轨数量: {len(cabinet_dict['rails'])}")
        
        # 测试转换功能
        actual_cabinet = allocator._convert_dict_to_actual_cabinet(cabinet_dict)
        
        if actual_cabinet:
            print("✅ 机柜字典转换成功")
            print(f"   - 机柜名称: {actual_cabinet.name}")
            print(f"   - 机柜类型: {actual_cabinet.cabinet_type}")
            print(f"   - 导轨数量: {len(actual_cabinet.rails)}")
            
            # 验证导轨信息
            for i, rail in enumerate(actual_cabinet.rails):
                print(f"   - 导轨{i+1}: {rail.name}")
                print(f"     * 长度: {rail.length}mm")
                print(f"     * 支持的器件类型: {rail.supported_part_types}")
                print(f"     * 可用长度: {rail.available_length}mm")
                
                # 测试find_compatible_rails方法是否存在
                if hasattr(actual_cabinet, 'find_compatible_rails'):
                    print(f"     * ✅ find_compatible_rails方法存在")
                else:
                    print(f"     * ❌ find_compatible_rails方法不存在")
            
            # 测试ActualCabinet的方法
            print("\n=== ActualCabinet方法测试 ===")
            
            # 测试get_rail_by_name
            rail_a = actual_cabinet.get_rail_by_name('Rail_A')
            if rail_a:
                print("✅ get_rail_by_name方法工作正常")
            else:
                print("❌ get_rail_by_name方法失败")
            
            # 创建一个测试器件来测试find_compatible_rails
            from core.component_allocation_models import ComponentDefinition, ComponentType
            
            test_component = ComponentDefinition(
                name="TestFTB",
                component_type=ComponentType.FIELD_TERM_IN,
                part_number="TEST-001",
                width=50.0
            )
            
            compatible_rails = actual_cabinet.find_compatible_rails(test_component)
            print(f"✅ find_compatible_rails方法工作正常，找到 {len(compatible_rails)} 个兼容导轨")
            
            return True
            
        else:
            print("❌ 机柜字典转换失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rail_instance_methods():
    """测试RailInstance的方法"""
    print("\n=== RailInstance方法测试 ===")
    
    try:
        from core.component_allocation_models import ComponentDefinition, ComponentType
        
        # 创建测试导轨
        rail = RailInstance(
            name="TestRail",
            position="Test",
            length=1000.0,
            io_type="Mixed",
            intrinsic="NIS",
            voltage_level=24,
            supported_part_types=["FieldTermIn", "FieldTermOut", "ETP"],
            reserved_from=0.0,
            reserved_to=50.0
        )
        
        print(f"📏 测试导轨: {rail.name}")
        print(f"   - 总长度: {rail.length}mm")
        print(f"   - 可用长度: {rail.available_length}mm")
        print(f"   - 下一个可用位置: {rail.next_available_position}mm")
        
        # 创建测试器件
        test_component = ComponentDefinition(
            name="TestComponent",
            component_type=ComponentType.FIELD_TERM_IN,
            part_number="TEST-001",
            width=100.0
        )
        
        # 测试can_accommodate方法
        can_accommodate = rail.can_accommodate(test_component)
        print(f"✅ can_accommodate方法: {can_accommodate}")
        
        if can_accommodate:
            # 测试allocate_component方法
            allocation = rail.allocate_component(test_component, "TestCabinet")
            print(f"✅ allocate_component方法成功")
            print(f"   - 分配位置: {allocation.position_start}mm - {allocation.position_end}mm")
            print(f"   - 导轨剩余空间: {rail.available_length}mm")
        
        return True
        
    except Exception as e:
        print(f"❌ RailInstance方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 机柜字典转换问题修复验证工具")
    print("=" * 50)
    
    # 测试机柜转换
    conversion_success = test_cabinet_conversion()
    
    # 测试导轨方法
    rail_success = test_rail_instance_methods()
    
    print("\n" + "=" * 50)
    if conversion_success and rail_success:
        print("🎉 所有测试通过！机柜字典转换修复成功！")
        print("📝 详细日志已保存到 test_cabinet_conversion.log")
        print("\n✅ 现在可以重新运行I/O点分配功能")
    else:
        print("⚠️  测试发现问题，请查看日志文件进行调试")
        print("📝 详细日志已保存到 test_cabinet_conversion.log")
