#!/usr/bin/env python3
"""
端到端分配流程调试脚本
模拟完整的I/O点分配过程，追踪每个步骤的详细状态
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger import setup_logger, get_logger
from core.allocator import IOAllocator
from core.data_models import IOPoint, SignalType
from core.component_allocation_engine import ComponentAllocationEngine
from core.wiring_typical_parser import WiringTypicalManager


def setup_debug_logging():
    """设置详细的调试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('debug_allocation_flow.log', mode='w', encoding='utf-8')
        ]
    )


def create_test_io_point():
    """创建测试I/O点"""
    return IOPoint(
        tag="1105-HS-10503A-1",
        cable_name="1105-C-10503A",
        pair_number=1,
        signal_type=SignalType.DI,
        is_intrinsic=False,
        system="1105",
        location="Field Area 1105",
        cable_type="Instrumentation",
        wiring_typical="DI NIS N",
        description="Test Digital Input Point"
    )


def test_typical_circuit_parsing():
    """测试典型回路解析"""
    print("=== 测试典型回路解析 ===")
    
    try:
        # 创建典型回路管理器
        typical_manager = WiringTypicalManager("data/wiring_typical")
        
        # 查找DI NIS N典型回路
        typical_name = "DI NIS N"
        typical_def = typical_manager.get_typical_by_name(typical_name)
        
        if not typical_def:
            print(f"❌ 未找到典型回路: {typical_name}")
            return False
        
        print(f"✅ 找到典型回路: {typical_name}")
        print(f"   信号类型: {typical_def.signal_type}")
        print(f"   本安类型: {typical_def.intrinsic}")
        print(f"   要求机柜类型: {typical_def.required_cabinet_type}")
        
        # 获取汇控柜器件
        marshalling_components = typical_def.get_marshalling_components()
        print(f"   汇控柜器件数量: {len(marshalling_components)}")
        
        for i, component in enumerate(marshalling_components, 1):
            print(f"     器件 {i}: {component.name}")
            print(f"       类型: {component.component_type.value}")
            print(f"       型号: {component.part_number}")
            print(f"       宽度: {component.width}mm")
            print(f"       安装方式: {component.mounting_type.value}")
        
        return typical_def
        
    except Exception as e:
        print(f"❌ 典型回路解析失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_component_allocation_engine():
    """测试器件分配引擎"""
    print("\n=== 测试器件分配引擎 ===")
    
    try:
        # 创建分配引擎
        engine = ComponentAllocationEngine("data/wiring_typical")
        
        # 创建测试I/O点
        io_point = create_test_io_point()
        print(f"测试I/O点: {io_point.tag}")
        print(f"  典型回路: {io_point.wiring_typical}")
        
        # 创建测试机柜（使用真实数据结构）
        from debug_real_data_loading import test_pidb_cabinet_loading, test_cabinet_template_loading, test_cabinet_conversion
        
        # 加载真实机柜数据
        cabinet_data = test_pidb_cabinet_loading()
        if not cabinet_data:
            print("❌ 无法加载机柜数据")
            return False
        
        enriched_cabinet = test_cabinet_template_loading(cabinet_data)
        if not enriched_cabinet:
            print("❌ 无法准备机柜数据")
            return False
        
        actual_cabinet = test_cabinet_conversion(enriched_cabinet)
        if not actual_cabinet:
            print("❌ 无法转换机柜数据")
            return False
        
        print(f"\n🏗️  使用机柜: {actual_cabinet.name}")
        print(f"   机柜类型: {actual_cabinet.cabinet_type}")
        print(f"   导轨数量: {len(actual_cabinet.rails)}")
        
        # 执行器件分配
        print(f"\n🔧 开始器件分配...")
        result = engine.allocate_io_point_components(
            io_point=io_point,
            actual_cabinet=actual_cabinet,
            wiring_typicals={}
        )
        
        print(f"\n📊 分配结果:")
        print(f"   成功: {result.success}")
        print(f"   分配的器件数: {len(result.component_allocations)}")
        print(f"   错误数: {len(result.errors)}")
        print(f"   警告数: {len(result.warnings)}")
        
        if result.errors:
            print(f"\n❌ 错误信息:")
            for error in result.errors:
                print(f"     - {error}")
        
        if result.warnings:
            print(f"\n⚠️  警告信息:")
            for warning in result.warnings:
                print(f"     - {warning}")
        
        if result.component_allocations:
            print(f"\n✅ 成功分配的器件:")
            for allocation in result.component_allocations:
                print(f"     - {allocation.component_definition.name} ({allocation.component_definition.component_type.value})")
                print(f"       机柜: {allocation.cabinet_name}")
                print(f"       导轨: {allocation.rail_name}")
                print(f"       位置: {allocation.position_start:.1f}mm - {allocation.position_end:.1f}mm")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 器件分配引擎测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_full_allocation_process():
    """测试完整的分配流程"""
    print("\n=== 测试完整分配流程 ===")
    
    try:
        # 创建分配器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        allocator = IOAllocator(config)
        
        # 创建测试I/O点
        io_point = create_test_io_point()
        
        # 模拟电缆数据
        cable_data = {
            'name': io_point.cable_name,
            'io_points': [io_point],
            'target_cabinet': '1105-SIS-REL-101'
        }
        
        # 加载机柜数据
        import pandas as pd
        pidb_file = list(Path("data/pidb").glob("*.xlsx"))[0]
        df = pd.read_excel(pidb_file, sheet_name='cabinet')
        cabinet_data = df.to_dict('records')
        
        # 转换机柜数据格式
        cabinets = []
        for cabinet_row in cabinet_data:
            cabinet = {
                'name': cabinet_row.get('Cabinet', ''),
                'template': cabinet_row.get('CabinetTemplate', ''),
                'type': 'Marshalling',
                'location': 'Unknown',
                'rails': [
                    {
                        'name': 'Rail_REL_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ] if 'REL' in cabinet_row.get('CabinetTemplate', '') else []
            }
            cabinets.append(cabinet)
        
        print(f"📋 准备分配数据:")
        print(f"   I/O点: {io_point.tag}")
        print(f"   电缆: {cable_data['name']}")
        print(f"   目标机柜: {cable_data['target_cabinet']}")
        print(f"   可用机柜数: {len(cabinets)}")
        
        # 执行分配
        print(f"\n🚀 开始完整分配流程...")
        result = allocator._allocate_cable(cable_data, cabinets, {})
        
        print(f"\n📊 最终分配结果:")
        print(f"   成功: {result.success}")
        print(f"   分配的点数: {len(result.allocated_points)}")
        print(f"   失败的点数: {len(result.failed_points)}")
        print(f"   错误数: {len(result.errors)}")
        print(f"   警告数: {len(result.warnings)}")
        
        if result.errors:
            print(f"\n❌ 错误信息:")
            for error in result.errors:
                print(f"     - {error}")
        
        if result.allocated_points:
            print(f"\n✅ 成功分配的I/O点:")
            for point in result.allocated_points:
                print(f"     - {point.tag}")
                if hasattr(point, 'allocated_cabinet'):
                    print(f"       机柜: {point.allocated_cabinet}")
                if hasattr(point, 'allocated_rail'):
                    print(f"       导轨: {point.allocated_rail}")
        
        return result.success
        
    except Exception as e:
        print(f"❌ 完整分配流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("开始端到端分配流程调试...")
    
    # 设置调试日志
    setup_debug_logging()
    
    test_results = []
    
    # 1. 测试典型回路解析
    typical_def = test_typical_circuit_parsing()
    test_results.append(typical_def is not False)
    
    # 2. 测试器件分配引擎
    if typical_def:
        engine_result = test_component_allocation_engine()
        test_results.append(engine_result)
    
    # 3. 测试完整分配流程
    full_result = test_full_allocation_process()
    test_results.append(full_result)
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 调试结果汇总 ===")
    print(f"成功: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过，分配流程正常")
    else:
        print("⚠️  部分测试失败，需要进一步检查")
        print("\n🔍 问题分析:")
        if not test_results[0]:
            print("- 典型回路解析失败")
        if len(test_results) > 1 and not test_results[1]:
            print("- 器件分配引擎失败")
        if len(test_results) > 2 and not test_results[2]:
            print("- 完整分配流程失败")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
