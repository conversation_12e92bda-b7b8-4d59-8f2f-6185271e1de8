#!/usr/bin/env python3
"""
测试数据模型创建修复
验证create_data_models方法正确使用PIDB机柜数据
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_data_model_creation_with_pidb():
    """测试使用PIDB数据创建数据模型"""
    print("=== 测试数据模型创建修复 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        # 创建数据加载器
        config = {}
        data_loader = DataLoader(config)
        
        # 模拟IODB数据
        iodb_data = {
            'io_points': [],
            'cables': {}
        }
        
        # 模拟PIDB数据（包含真实机柜名称）
        pidb_data = {
            'cabinets': [
                {
                    'name': '1105-SIS-SYS-101',  # 真实机柜名称
                    'template': 'PPG SYS',       # 模板名称
                    'type': 'System',
                    'location': 'Control Room 1105',
                    'source': 'PIDB'
                },
                {
                    'name': '1105-SIS-BAR-101',  # 真实机柜名称
                    'template': 'PPG BAR',       # 模板名称
                    'type': 'Marshalling',
                    'location': 'Field Area 1105',
                    'source': 'PIDB'
                },
                {
                    'name': '1105-SIS-REL-101',  # 真实机柜名称
                    'template': 'PPG REL',       # 模板名称
                    'type': 'Marshalling',
                    'location': 'Field Area 1105',
                    'source': 'PIDB'
                },
                {
                    'name': '1105-SIS-RIO-001',  # 真实机柜名称
                    'template': 'PPG RIO',       # 模板名称
                    'type': 'System/Marshalling',
                    'location': 'Field Area 1105',
                    'source': 'PIDB'
                }
            ]
        }
        
        # 模拟机柜配置文件（模板配置）
        cabinet_profiles = {
            'PPG SYS': {
                'type': 'System',
                'rails': [
                    {
                        'name': 'Rail_SYS_01',
                        'position': 'System',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'ETP',
                        'PartType02': 'IOModule',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG BAR': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_MAR_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG REL': {
                'type': 'Marshalling',
                'rails': [
                    {
                        'name': 'Rail_REL_01',
                        'position': 'Marshalling',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'Barrier',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            },
            'PPG RIO': {
                'type': 'System/Marshalling',
                'rails': [
                    {
                        'name': 'Rail_RIO_01',
                        'position': 'Mixed',
                        'length': 2000.0,
                        'io_type': 'Mixed',
                        'intrinsic': 'NIS',
                        'voltage_level': 24,
                        'PartType01': 'FieldTermIn',
                        'PartType02': 'FieldTermOut',
                        'PartType03': 'ETP',
                        'PartType04': 'IOModule',
                        'reserved_from': 0.0,
                        'reserved_to': 0.0
                    }
                ]
            }
        }
        
        # 模拟典型回路
        wiring_typicals = {}
        
        print(f"测试场景:")
        print(f"  PIDB机柜数量: {len(pidb_data['cabinets'])}")
        print(f"  机柜配置模板数量: {len(cabinet_profiles)}")
        
        # 创建数据模型
        print(f"\n开始创建数据模型...")
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            wiring_typicals
        )
        
        # 分析结果
        print(f"\n=== 数据模型创建结果分析 ===")
        cabinets = models.get('cabinets', [])
        print(f"创建的机柜数量: {len(cabinets)}")
        
        if cabinets:
            print(f"\n创建的机柜详情:")
            success_count = 0
            for i, cabinet in enumerate(cabinets, 1):
                name = cabinet.get('name', 'Unknown')
                template = cabinet.get('template', 'Unknown')
                cabinet_type = cabinet.get('type', 'Unknown')
                source = cabinet.get('source', 'Unknown')
                rails_count = len(cabinet.get('rails', []))
                
                print(f"  {i}. 机柜名称: {name}")
                print(f"     模板名称: {template}")
                print(f"     机柜类型: {cabinet_type}")
                print(f"     数据来源: {source}")
                print(f"     导轨数量: {rails_count}")
                
                # 验证是否使用了真实机柜名称
                if name.startswith('1105-SIS-'):
                    print(f"     ✅ 使用真实机柜名称")
                    success_count += 1
                elif name in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']:
                    print(f"     ❌ 仍使用模板名称")
                else:
                    print(f"     ⚠️  未知名称格式")
                
                print()
            
            print(f"使用真实机柜名称的比例: {success_count}/{len(cabinets)}")
            return success_count == len(cabinets)
        else:
            print(f"❌ 未创建任何机柜")
            return False
        
    except Exception as e:
        print(f"❌ 数据模型创建测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_to_config():
    """测试回退到配置文件的情况"""
    print("\n=== 测试回退到配置文件 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        
        data_loader = DataLoader({})
        
        # 模拟空的PIDB数据
        iodb_data = {'io_points': [], 'cables': {}}
        pidb_data = {'cabinets': []}  # 空的机柜列表
        
        # 机柜配置文件
        cabinet_profiles = {
            'PPG BAR': {'type': 'Marshalling', 'rails': []},
            'PPG SYS': {'type': 'System', 'rails': []}
        }
        
        wiring_typicals = {}
        
        print(f"测试场景: PIDB无机柜数据，应回退到配置文件")
        
        # 创建数据模型
        models = data_loader.create_data_models(
            iodb_data,
            pidb_data,
            cabinet_profiles,
            wiring_typicals
        )
        
        cabinets = models.get('cabinets', [])
        print(f"创建的机柜数量: {len(cabinets)}")
        
        if cabinets:
            for cabinet in cabinets:
                name = cabinet.get('name', 'Unknown')
                source = cabinet.get('source', 'Unknown')
                print(f"  - {name} (来源: {source})")
                
                # 验证回退逻辑
                if source == 'Config':
                    print(f"    ✅ 正确回退到配置文件")
                else:
                    print(f"    ❌ 回退逻辑错误")
        
        return len(cabinets) > 0
        
    except Exception as e:
        print(f"❌ 回退测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始测试数据模型创建修复...")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_data_model_creation_with_pidb())
    test_results.append(test_fallback_to_config())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 最终测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！数据模型创建修复成功。")
        print("\n🔧 修复总结：")
        print("✅ 1. create_data_models方法现在优先使用PIDB机柜数据")
        print("✅ 2. 机柜对象使用真实机柜名称而不是模板名称")
        print("✅ 3. 保留模板名称用于配置查找")
        print("✅ 4. 提供回退机制，无PIDB数据时使用配置文件")
        print("✅ 5. 添加数据来源标记便于调试")
        
        print("\n📋 修复效果：")
        print("现在分配器将接收到真实机柜名称：")
        print("✅ 1105-SIS-SYS-101 (而不是 PPG SYS)")
        print("✅ 1105-SIS-BAR-101 (而不是 PPG BAR)")
        print("✅ 1105-SIS-REL-101 (而不是 PPG REL)")
        print("✅ 1105-SIS-RIO-001 (而不是 PPG RIO)")
        
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
