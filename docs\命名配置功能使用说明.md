# 灵活命名配置功能使用说明

## 功能概述

全新的灵活命名配置功能允许用户自由组合各种命名元素，创建完全自定义的器件命名规则。支持15种不同的命名元素，包括固定文本、Tag名、机架编号、槽位编号、通道号、机柜信息、电缆信息、ETP信息、信号类型和卡件型号等。用户可以通过拖拽和组合的方式，构建符合项目特定要求的命名规则。

## 功能位置

在I/O分配界面的操作面板中，您会看到一个新的"命名规则"按钮，位于"IO分配表"按钮旁边。

## 使用步骤

### 1. 打开命名配置对话框

1. 在I/O分配界面点击"命名规则"按钮
2. 系统将打开"命名规则配置"对话框

### 2. 灵活配置器件命名规则

对话框采用全新的分屏设计，包含6个标签页，分别对应不同的器件类型：

- **安全栅**：用于配置安全栅的命名规则
- **继电器**：用于配置继电器的命名规则
- **隔离器**：用于配置隔离器的命名规则
- **防雷栅**：用于配置防雷栅的命名规则
- **端子排**：用于配置端子排的命名规则
- **TR端子排**：用于配置TR端子排的命名规则

每个标签页采用左右分屏布局：

#### 左侧：可用命名元素
显示所有可用的命名元素，包括：

- **固定文本**：用户自定义的固定文本
- **Tag名**：IO点的Tag名称
- **机架编号**：机架编号（1-2位，如：5, 10）
- **槽位编号**：槽位编号（1位）
- **通道号**：通道号（2位格式，如：04）
- **机柜编号**：机柜编号
- **导轨号**：典型机柜Rail_后的内容
- **器件编号**：器件在导轨上的编号
- **机柜名**：完整机柜名（如：1103-SIS-SYS-101）
- **简化机柜名**：简化机柜名（如：SYS-101）
- **电缆名**：电缆名称
- **ETP名**：ETP名称
- **ETP上/下卡**：ETP上/下卡后缀（默认U/L，可自定义）
- **信号类型**：信号类型（AI/AO/DI/DO）
- **卡件型号**：卡件型号

#### 右侧：当前命名规则
- **规则列表**：显示当前选择的命名元素序列
- **操作按钮**：上移、下移、移除元素
- **元素编辑区**：编辑选中元素的属性
  - **固定文本编辑**：可以修改固定文本的内容
  - **ETP后缀选择**：选择上卡(U)、下卡(L)或自定义后缀
  - **更新按钮**：应用编辑的更改
- **实时预览**：显示当前规则的命名效果

### 3. 编辑命名元素

选择规则列表中的元素后，可以在元素编辑区进行修改：

#### 编辑固定文本
1. 在规则列表中选择固定文本元素
2. 在"固定文本"输入框中修改内容
3. 点击"更新元素"按钮应用更改
4. 固定文本会对所有tag保持一致，不会因tag不同而变化

#### 编辑ETP后缀
1. 在规则列表中选择ETP后缀元素
2. 在"ETP后缀"下拉框中选择：
   - **U (上卡)**：使用标准上卡后缀
   - **L (下卡)**：使用标准下卡后缀
   - **自定义...**：输入自定义后缀内容
3. 点击"更新元素"按钮应用更改

### 4. 实时预览

在每个标签页的"预览"区域，您可以实时查看当前配置的命名效果：

- 当您添加、移除或编辑元素时，预览会自动更新
- 预览显示实际的命名示例，帮助您验证配置是否正确

### 4. 全局预览

点击"预览"按钮可以查看所有器件类型的命名规则预览，方便您统一检查所有配置。

### 5. 重置为默认

如果需要恢复默认设置，点击"重置为默认"按钮：

- 系统会将所有器件的命名规则重置为默认值
- 默认前缀：BA（安全栅）、RY（继电器）、ISL（隔离器）、SP（防雷栅）、TB（端子排）、TR（TR端子排）
- 默认分隔符和后缀：均为空

### 6. 保存配置

1. 配置完成后，点击"确定"按钮保存设置
2. 系统会将配置保存到 `config/naming_rules.json` 文件
3. 保存成功后会显示确认消息

## 命名规则说明

### 默认命名元素

不同器件类型使用不同的命名元素组合：

#### 安全栅/继电器/隔离器/防雷栅
- **格式**：前缀 + 机架编号 + 槽位编号 + 通道编号（2位）
- **示例**：BA1305（安全栅，机架1，槽位3，通道05）

#### 端子排
- **格式**：前缀 + 机柜编号 + 导轨编号 + 器件编号
- **示例**：TB02F03（端子排，机柜02，导轨F，器件03）

#### TR端子排
- **格式**：前缀 + ETP名称
- **示例**：TRR1S3U（TR端子排，ETP名称R1S3U）

### 自定义示例

以下是一些灵活命名配置示例：

#### 示例1：传统命名规则
- **元素组合**：固定文本(BA) + 机架编号 + 槽位编号 + 通道号
- **结果**：BA1305

#### 示例2：带分隔符的命名
- **元素组合**：固定文本(SAFETY) + 固定文本(_) + 机架编号 + 固定文本(_) + 槽位编号 + 固定文本(_) + 通道号
- **结果**：SAFETY_1_3_05

#### 示例3：包含Tag名的命名
- **元素组合**：Tag名 + 固定文本(_) + 信号类型 + 固定文本(_) + 机架编号
- **结果**：AI_TEMP_001_AI_1

#### 示例4：复杂机柜信息命名
- **元素组合**：固定文本(TB) + 机柜编号 + 导轨号 + 器件编号 + 固定文本(_) + 简化机柜名
- **结果**：TB02F03_SYS-101

#### 示例5：ETP相关命名
- **元素组合**：固定文本(TR) + ETP名 + ETP后缀(U)
- **结果**：TRR1S3U

#### 示例6：自定义ETP后缀
- **元素组合**：固定文本(ETP) + 固定文本(_) + ETP名 + 固定文本(_) + ETP后缀(TOP)
- **结果**：ETP_R1S3_TOP

## 应用配置

配置保存后，当您导出IO分配表时，系统会自动应用您的自定义命名规则：

1. 点击"IO分配表"按钮导出报表
2. 系统读取您保存的命名配置
3. 根据配置生成相应的器件编号
4. 在生成的Excel报表中应用自定义命名

## 配置文件

命名规则配置保存在 `config/naming_rules.json` 文件中，格式如下：

```json
{
  "barrier": {
    "prefix": "BA",
    "separator": "",
    "suffix": ""
  },
  "relay": {
    "prefix": "RY",
    "separator": "_",
    "suffix": ""
  },
  "isolator": {
    "prefix": "ISL",
    "separator": "",
    "suffix": "_END"
  },
  "surge_protector": {
    "prefix": "SP",
    "separator": "-",
    "suffix": ""
  },
  "terminal_block": {
    "prefix": "TB",
    "separator": "",
    "suffix": ""
  },
  "tr_terminal": {
    "prefix": "TR",
    "separator": "",
    "suffix": ""
  }
}
```

## 注意事项

1. **配置持久性**：配置会自动保存，下次打开软件时会自动加载您的设置
2. **预览准确性**：预览显示的是实际的命名效果，与最终生成的报表一致
3. **配置验证**：系统会验证配置的有效性，确保生成的命名符合要求
4. **备份建议**：建议定期备份 `config/naming_rules.json` 文件，以防配置丢失

## 故障排除

如果遇到问题，请检查：

1. **对话框无法打开**：检查是否有其他对话框正在显示
2. **配置无法保存**：检查 `config` 目录是否有写入权限
3. **预览不更新**：尝试重新输入配置内容
4. **命名不生效**：确认配置已保存，并重新导出报表

## 🎯 完整使用流程

现在用户可以：

1. **打开配置**：在分配界面点击"命名规则"按钮
2. **选择器件**：在标签页中选择要配置的器件类型
3. **添加元素**：从左侧选择元素，点击"添加 →"按钮
4. **编辑元素**：
   - 在右侧规则列表中选择要编辑的元素
   - 对于固定文本：在编辑框中修改内容
   - 对于ETP后缀：选择上卡/下卡或输入自定义后缀
   - 点击"更新元素"按钮应用更改
5. **调整顺序**：使用上移、下移按钮调整元素顺序
6. **移除元素**：选择不需要的元素，点击"移除"按钮
7. **预览效果**：查看右下角的实时预览
8. **全局预览**：点击"预览所有规则"查看所有器件的命名效果
9. **保存配置**：点击"确定"保存设置
10. **应用规则**：导出IO分配表时自动应用自定义规则

## 💡 使用技巧

- **固定文本的灵活运用**：可以用作前缀、后缀、分隔符或任何固定标识
- **组合使用**：通过多个固定文本元素可以创建复杂的命名模式
- **实时反馈**：每次修改都会立即在预览中显示效果
- **批量配置**：可以为不同器件类型设置不同的命名规则
- **配置复用**：保存的配置会在下次打开时自动加载

## 技术支持

如需更多帮助，请查看日志文件或联系技术支持。
