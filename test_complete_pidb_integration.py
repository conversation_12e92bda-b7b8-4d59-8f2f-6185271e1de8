#!/usr/bin/env python3
"""
完整的PIDB集成测试
验证从PIDB读取到分配器使用的完整数据流
"""

import sys
import os
from pathlib import Path
import pandas as pd
import tempfile

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def create_test_pidb_file():
    """创建测试用的PIDB文件"""
    # 创建临时Excel文件
    temp_file = tempfile.NamedTemporaryFile(suffix='.xlsx', delete=False)
    temp_file.close()
    
    # 创建PIDB cabinet工作表数据（基于提供的截图）
    cabinet_data = {
        'Cabinet': ['1105-SIS-SYS-101', '1105-SIS-BAR-101', '1105-SIS-REL-101', '1105-SIS-RIO-001'],
        'CabinetTemplate': ['PPG SYS', 'PPG BAR', 'PPG REL', 'PPG RIO'],
        'CabinetNo': ['01', '02', '03', '01']
    }
    
    # 写入Excel文件
    with pd.ExcelWriter(temp_file.name, engine='openpyxl') as writer:
        df = pd.DataFrame(cabinet_data)
        df.to_excel(writer, sheet_name='cabinet', index=False)
    
    return temp_file.name

def test_complete_pidb_integration():
    """测试完整的PIDB集成流程"""
    print("=== 测试完整的PIDB集成流程 ===")
    
    try:
        from core.data_loader_simple import DataLoader
        from core.allocator import IOAllocator
        from core.data_models import IOPoint, SignalType
        
        # 创建测试PIDB文件
        pidb_file = create_test_pidb_file()
        print(f"创建测试PIDB文件: {pidb_file}")
        
        # 创建数据加载器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        
        data_loader = DataLoader(config)
        
        # 加载PIDB数据
        print(f"\n开始加载PIDB数据...")
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        print(f"PIDB数据加载结果:")
        print(f"  机柜数量: {len(pidb_data.get('cabinets', []))}")
        print(f"  机架数量: {len(pidb_data.get('racks', []))}")
        
        # 检查机柜数据
        cabinets = pidb_data.get('cabinets', [])
        if cabinets:
            print(f"\n加载的机柜列表:")
            for cabinet in cabinets:
                name = cabinet.get('name', 'Unknown')
                template = cabinet.get('template', 'Unknown')
                cabinet_type = cabinet.get('type', 'Unknown')
                source = cabinet.get('source', 'Unknown')
                print(f"  - {name} (模板: {template}, 类型: {cabinet_type}, 来源: {source})")
                
                # 验证是否使用了真实机柜名称
                if name.startswith('1105-SIS-'):
                    print(f"    ✅ 使用真实机柜名称")
                elif name in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']:
                    print(f"    ❌ 仍使用模板名称")
                    return False
                else:
                    print(f"    ⚠️  未知名称格式")
        
        # 创建分配器并测试分配
        print(f"\n开始测试分配流程...")
        allocator = IOAllocator(config)
        
        # 创建测试I/O点
        cables = [
            {
                'name': 'Cable_Test',
                'io_points': [
                    IOPoint(
                        tag="1105-HS-10503B-2",
                        signal_type=SignalType.DI,
                        description="Test Hand Switch",
                        wiring_typical="DI NIS N",
                        location="Field Area 1105"
                    )
                ]
            }
        ]
        
        # 执行分配
        result = allocator.allocate_io_points(cables, cabinets, {})
        
        print(f"\n=== 分配结果分析 ===")
        print(f"分配成功: {result.success}")
        print(f"成功分配: {len(result.allocated_points)}")
        print(f"失败分配: {len(result.failed_points)}")
        
        # 检查分配结果中的机柜名称
        name_correct = False
        if result.allocated_points:
            for io_point in result.allocated_points:
                cabinet_name = getattr(io_point, 'cabinet', 'Unknown')
                print(f"  - {io_point.tag} 分配到机柜: {cabinet_name}")
                
                # 验证机柜名称是真实名称而不是模板名称
                if cabinet_name.startswith('1105-SIS-'):
                    print(f"    ✅ 分配结果使用真实机柜名称")
                    name_correct = True
                elif cabinet_name in ['PPG BAR', 'PPG SYS', 'PPG RIO', 'PPG REL']:
                    print(f"    ❌ 分配结果仍使用模板名称")
                    name_correct = False
                else:
                    print(f"    ⚠️  分配结果使用未知名称格式: {cabinet_name}")
        
        # 检查错误和警告信息
        template_warning_found = False
        if result.errors:
            print(f"\n错误信息:")
            for error in result.errors:
                print(f"  - {error}")
                if any(template in error for template in ['PPG BAR', 'PPG SYS', 'PPG RIO']):
                    print(f"    ❌ 错误信息仍显示模板名称")
                    template_warning_found = True
                elif any(real_name in error for real_name in ['1105-SIS-SYS-101', '1105-SIS-BAR-101', '1105-SIS-REL-101', '1105-SIS-RIO-001']):
                    print(f"    ✅ 错误信息显示真实机柜名称")
        
        if result.warnings:
            print(f"\n警告信息:")
            for warning in result.warnings:
                print(f"  - {warning}")
                if any(template in warning for template in ['PPG BAR', 'PPG SYS', 'PPG RIO']):
                    print(f"    ❌ 警告信息仍显示模板名称")
                    template_warning_found = True
                elif any(real_name in warning for real_name in ['1105-SIS-SYS-101', '1105-SIS-BAR-101', '1105-SIS-REL-101', '1105-SIS-RIO-001']):
                    print(f"    ✅ 警告信息显示真实机柜名称")
        
        # 清理临时文件
        os.unlink(pidb_file)
        
        # 返回测试结果
        return len(cabinets) > 0 and name_correct and not template_warning_found
        
    except Exception as e:
        print(f"❌ 完整PIDB集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_pidb_file():
    """测试真实的PIDB文件（如果存在）"""
    print("\n=== 测试真实PIDB文件 ===")
    
    try:
        # 查找真实的PIDB文件
        pidb_paths = [
            "04B_PIDB/PIDB PPG.xlsx",
            "PIDB PPG.xlsx",
            "data/PIDB PPG.xlsx"
        ]
        
        pidb_file = None
        for path in pidb_paths:
            if Path(path).exists():
                pidb_file = path
                break
        
        if not pidb_file:
            print("未找到真实的PIDB文件，跳过此测试")
            return True
        
        print(f"找到PIDB文件: {pidb_file}")
        
        from core.data_loader_simple import DataLoader
        
        data_loader = DataLoader({})
        pidb_data = data_loader.load_pidb_data(pidb_file)
        
        cabinets = pidb_data.get('cabinets', [])
        print(f"从真实PIDB文件加载的机柜数量: {len(cabinets)}")
        
        if cabinets:
            print("真实PIDB文件中的机柜:")
            for cabinet in cabinets:
                name = cabinet.get('name', 'Unknown')
                template = cabinet.get('template', 'Unknown')
                source = cabinet.get('source', 'Unknown')
                print(f"  - {name} (模板: {template}, 来源: {source})")
        
        return True
        
    except Exception as e:
        print(f"❌ 真实PIDB文件测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("开始完整的PIDB集成测试...")
    
    test_results = []
    
    # 执行测试
    test_results.append(test_complete_pidb_integration())
    test_results.append(test_real_pidb_file())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 最终测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！PIDB机柜名称问题完全修复。")
        print("\n🔧 修复总结：")
        print("✅ 1. 实现了正确的PIDB cabinet工作表读取")
        print("✅ 2. 建立了模板名称到真实名称的映射关系")
        print("✅ 3. 确保分配器使用真实机柜名称")
        print("✅ 4. 日志信息显示真实机柜名称而不是模板名称")
        
        print("\n📋 修复前后对比：")
        print("❌ 修复前:")
        print("   - 系统使用模板名称: PPG BAR, PPG SYS, PPG RIO")
        print("   - 日志显示: '器件 FTB1 在机柜 PPG BAR 中无兼容导轨'")
        print("✅ 修复后:")
        print("   - 系统使用真实名称: 1105-SIS-BAR-101, 1105-SIS-SYS-101, 1105-SIS-RIO-001")
        print("   - 日志显示: '器件 FTB1 在机柜 1105-SIS-BAR-101 中无兼容导轨'")
        
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
