"""
MarshallingCabinet节点修复验证脚本
测试解析器是否能正确处理System-MarshallingCabinet节点
"""

import logging
from pathlib import Path
from core.wiring_typical_parser import WiringTypicalManager, WiringTypicalParser

def setup_test_logging():
    """设置测试日志"""
    logging.basicConfig(
        level=logging.DEBUG,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.StreamHandler(),
            logging.FileHandler('test_marshalling_cabinet_fix.log', mode='w', encoding='utf-8')
        ]
    )

def test_problematic_typical_circuits():
    """测试有问题的典型回路"""
    print("🔧 MarshallingCabinet节点修复验证")
    print("=" * 50)
    
    # 设置测试日志
    setup_test_logging()
    
    # 问题典型回路列表
    problematic_circuits = [
        "DI NIS REMOTE.xml",
        "DO NIS WET REMOTE.xml"
    ]
    
    parser = WiringTypicalParser()
    typical_dir = Path("01C_Wiring Typical")
    
    success_count = 0
    total_count = len(problematic_circuits)
    
    for circuit_file in problematic_circuits:
        print(f"\n📋 测试典型回路: {circuit_file}")
        
        file_path = typical_dir / circuit_file
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            continue
        
        try:
            # 解析典型回路
            typical_def = parser.parse_wiring_typical_file(str(file_path))
            
            print(f"✅ 解析成功: {typical_def.name}")
            print(f"   - 信号类型: {typical_def.signal_type}")
            print(f"   - 本安类型: {typical_def.intrinsic}")
            print(f"   - 汇控柜器件数: {len(typical_def.marshalling_components)}")
            print(f"   - 系统柜器件数: {len(typical_def.system_components)}")
            print(f"   - Wire数量: {len(typical_def.wires)}")
            
            # 显示汇控柜器件详情
            if typical_def.marshalling_components:
                print("   📦 汇控柜器件:")
                for i, component in enumerate(typical_def.marshalling_components[:5]):  # 只显示前5个
                    print(f"     {i+1}. {component.name} ({component.component_type.value})")
                    print(f"        - 型号: {component.part_number}")
                    print(f"        - 尺寸: {component.width or component.length or 'N/A'}mm")
                
                if len(typical_def.marshalling_components) > 5:
                    print(f"     ... 还有 {len(typical_def.marshalling_components) - 5} 个器件")
            else:
                print("   ⚠️  没有找到汇控柜器件")
            
            # 显示Wire连接信息
            if typical_def.wires:
                print("   🔗 Wire连接:")
                for i, wire in enumerate(typical_def.wires[:3]):  # 只显示前3个
                    print(f"     {i+1}. {wire.get('name', 'Unnamed')}")
                    print(f"        - End1: {wire.get('end1', 'N/A')}")
                    print(f"        - End2: {wire.get('end2', 'N/A')}")
                
                if len(typical_def.wires) > 3:
                    print(f"     ... 还有 {len(typical_def.wires) - 3} 个Wire连接")
            
            success_count += 1
            
        except Exception as e:
            print(f"❌ 解析失败: {e}")
            import traceback
            traceback.print_exc()
    
    print(f"\n" + "=" * 50)
    print(f"📊 测试结果: {success_count}/{total_count} 个典型回路解析成功")
    
    return success_count == total_count

def test_all_typical_circuits():
    """测试所有典型回路，确保修复没有破坏其他功能"""
    print("\n🔧 全面典型回路测试")
    print("=" * 50)
    
    try:
        manager = WiringTypicalManager("01C_Wiring Typical")
        typicals = manager.load_all_typicals()
        
        print(f"✅ 成功加载 {len(typicals)} 个典型回路")
        
        # 统计不同类型的机柜节点
        marshalling_count = 0
        system_marshalling_count = 0
        no_marshalling_count = 0
        
        for name, typical in typicals.items():
            if len(typical.marshalling_components) > 0:
                # 检查是否使用了System-MarshallingCabinet
                if any("System-MarshallingCabinet" in str(comp.properties) for comp in typical.marshalling_components):
                    system_marshalling_count += 1
                else:
                    marshalling_count += 1
            else:
                no_marshalling_count += 1
        
        print(f"📊 机柜节点类型统计:")
        print(f"   - 标准MarshallingCabinet: {marshalling_count}")
        print(f"   - System-MarshallingCabinet: {system_marshalling_count}")
        print(f"   - 无汇控柜器件: {no_marshalling_count}")
        
        # 显示有器件的典型回路
        print(f"\n📋 有汇控柜器件的典型回路:")
        for name, typical in typicals.items():
            if len(typical.marshalling_components) > 0:
                print(f"   - {name}: {len(typical.marshalling_components)} 个器件")
        
        return True
        
    except Exception as e:
        print(f"❌ 全面测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_allocation_with_fixed_circuits():
    """测试修复后的典型回路是否能正常进行器件分配"""
    print("\n🔧 器件分配测试")
    print("=" * 50)
    
    try:
        from core.data_models import IOPoint, SignalType
        from core.allocator import IOAllocator
        
        # 创建测试I/O点，使用修复的典型回路
        test_io_points = [
            IOPoint(
                tag="TEST-DI-REMOTE-001",
                signal_type=SignalType.DI,
                description="Test DI Remote",
                location="Test Location",
                cable_name="TEST-CABLE-001",
                pair_number=1,
                is_intrinsic=False,
                system="Test System",
                cable_type="Control",
                wiring_typical="DI NIS REMOTE"
            ),
            IOPoint(
                tag="TEST-DO-REMOTE-001",
                signal_type=SignalType.DO,
                description="Test DO Remote",
                location="Test Location",
                cable_name="TEST-CABLE-002",
                pair_number=1,
                is_intrinsic=False,
                system="Test System",
                cable_type="Control",
                wiring_typical="DO NIS WET REMOTE"
            )
        ]
        
        # 创建分配器
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        
        allocator = IOAllocator(config)
        
        # 创建测试机柜
        cabinet_dict = {
            'name': 'E-2001',
            'template': 'System Cabinet',
            'type': 'System/Marshalling',
            'location': 'Test Area',
            'rails': [
                {
                    'name': 'Rail_SYS',
                    'position': 'System',
                    'length': 2000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'Barrier',
                    'PartType05': 'IOModule',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        }
        
        success_count = 0
        
        for io_point in test_io_points:
            print(f"\n📍 测试I/O点: {io_point.tag}")
            print(f"   - 典型回路: {io_point.wiring_typical}")
            
            try:
                # 测试单点分配
                success = allocator._allocate_single_point(io_point, cabinet_dict, {})
                
                if success:
                    print(f"✅ 分配成功")
                    print(f"   - 分配状态: {io_point.allocation_status}")
                    print(f"   - 分配机柜: {io_point.allocated_cabinet}")
                    
                    # 检查器件分配信息
                    if hasattr(io_point, 'component_allocations') and io_point.component_allocations:
                        print(f"   - 分配器件数: {len(io_point.component_allocations)}")
                        for i, allocation in enumerate(io_point.component_allocations[:3]):  # 只显示前3个
                            if hasattr(allocation, 'component_name'):
                                print(f"     * 器件{i+1}: {allocation.component_name}")
                    
                    success_count += 1
                else:
                    print(f"❌ 分配失败")
                    
            except Exception as e:
                print(f"❌ 分配异常: {e}")
        
        print(f"\n📊 器件分配测试结果: {success_count}/{len(test_io_points)} 个I/O点分配成功")
        return success_count == len(test_io_points)
        
    except Exception as e:
        print(f"❌ 器件分配测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 MarshallingCabinet节点修复验证工具")
    print("解决问题: 支持System-MarshallingCabinet节点")
    print("=" * 60)
    
    # 测试1: 问题典型回路
    problematic_success = test_problematic_typical_circuits()
    
    # 测试2: 全面典型回路测试
    if problematic_success:
        all_circuits_success = test_all_typical_circuits()
    else:
        all_circuits_success = False
    
    # 测试3: 器件分配测试
    if all_circuits_success:
        allocation_success = test_component_allocation_with_fixed_circuits()
    else:
        allocation_success = False
    
    print("\n" + "=" * 60)
    if problematic_success and all_circuits_success and allocation_success:
        print("🎉 所有测试通过！MarshallingCabinet节点修复成功！")
        print("✅ System-MarshallingCabinet节点现在可以正确解析")
        print("✅ 器件分配功能正常工作")
        print("📝 详细日志已保存到 test_marshalling_cabinet_fix.log")
    else:
        print("⚠️  测试发现问题:")
        print(f"   - 问题典型回路测试: {'✅' if problematic_success else '❌'}")
        print(f"   - 全面典型回路测试: {'✅' if all_circuits_success else '❌'}")
        print(f"   - 器件分配测试: {'✅' if allocation_success else '❌'}")
        print("📝 详细日志已保存到 test_marshalling_cabinet_fix.log")
