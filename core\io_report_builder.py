"""
IO分配表构建器
负责生成IO分配报表，特别处理spare点的显示格式
"""

import logging
from typing import Dict, List, Any, Optional, TYPE_CHECKING
from dataclasses import dataclass

from core.logger import get_logger
from core.data_models import IOPoint

if TYPE_CHECKING:
    from core.report_generator import ReportTemplate

# 内部使用的简化模板类
@dataclass
class _ReportTemplate:
    """报表模板 - 内部使用"""
    template_path: str
    name: str
    description: str = ""


class IOReportBuilder:
    """IO分配表构建器"""
    
    def __init__(self, template: "ReportTemplate"):
        """
        初始化IO分配表构建器
        
        Args:
            template: 报表模板
        """
        self.template = template
        self.logger = get_logger(__name__)
        self.data_sources = {}
    
    def add_data_source(self, name: str, data: Any):
        """
        添加数据源
        
        Args:
            name: 数据源名称
            data: 数据内容
        """
        self.data_sources[name] = data
        self.logger.debug(f"添加数据源: {name}")
    
    def validate_data_sources(self) -> List[str]:
        """
        验证数据源完整性
        
        Returns:
            错误列表
        """
        errors = []
        required_sources = ['allocation_result', 'pidb_data', 'wiring_typicals', 'naming_rules']
        
        for source in required_sources:
            if source not in self.data_sources:
                errors.append(f"缺少必需的数据源: {source}")
        
        return errors
    
    def generate_report(self, output_path: str) -> bool:
        """
        生成IO分配报表
        
        Args:
            output_path: 输出文件路径
            
        Returns:
            生成是否成功
        """
        try:
            self.logger.info(f"开始生成IO分配报表: {output_path}")
            
            # 验证数据源
            errors = self.validate_data_sources()
            if errors:
                for error in errors:
                    self.logger.error(error)
                return False
            
            # 获取分配结果
            allocation_result = self.data_sources.get('allocation_result')
            if not allocation_result:
                self.logger.error("分配结果为空")
                return False
            
            # 处理IO点数据，特别处理spare点
            processed_points = self._process_io_points(allocation_result.allocated_points)

            # 生成实际的Excel文件
            success = self._generate_excel_report(processed_points, output_path)
            if not success:
                return False

            # 统计spare点
            spare_count = sum(1 for point in processed_points if point.get('is_spare', False))
            regular_count = len(processed_points) - spare_count

            self.logger.info(f"常规IO点: {regular_count}, Spare点: {spare_count}")
            self.logger.info("IO分配报表生成完成")

            return True
            
        except Exception as e:
            self.logger.error(f"生成IO分配报表失败: {e}")
            return False
    
    def _process_io_points(self, io_points: List[IOPoint]) -> List[Dict[str, Any]]:
        """
        处理IO点数据，特别处理spare点的显示格式
        
        Args:
            io_points: IO点列表
            
        Returns:
            处理后的IO点数据
        """
        processed_points = []
        
        for point in io_points:
            processed_point = {
                'tag': self._format_tag_for_display(point),
                'description': self._format_description_for_display(point),
                'signal_type': point.signal_type.value if hasattr(point.signal_type, 'value') else str(point.signal_type),
                'location': point.location,
                'cabinet': point.cabinet,
                'rack': point.rack,
                'slot': point.slot,
                'channel': point.channel,
                'cable_name': point.cable_name,
                'pair_number': point.pair_number,
                'is_intrinsic': point.is_intrinsic,
                'system': point.system,
                'cable_type': point.cable_type,
                'wiring_typical': point.wiring_typical,
                'is_spare': getattr(point, 'is_spare', False)
            }
            
            processed_points.append(processed_point)
        
        return processed_points
    
    def _format_tag_for_display(self, point: IOPoint) -> str:
        """
        格式化Tag名用于显示
        对于spare点，去掉数字后缀和下划线，显示为"SPARE"
        
        Args:
            point: IO点对象
            
        Returns:
            格式化后的Tag名
        """
        if hasattr(point, 'is_spare') and point.is_spare:
            # 对于spare点，显示为"SPARE"
            return "SPARE"
        else:
            # 对于常规点，显示原始tag名
            return point.tag
    
    def _format_description_for_display(self, point: IOPoint) -> str:
        """
        格式化描述用于显示
        对于spare点，统一显示为"-"
        
        Args:
            point: IO点对象
            
        Returns:
            格式化后的描述
        """
        if hasattr(point, 'is_spare') and point.is_spare:
            # 对于spare点，描述统一为"-"
            return "-"
        else:
            # 对于常规点，显示原始描述
            return point.description

    def _generate_excel_report(self, processed_points: List[Dict[str, Any]], output_path: str) -> bool:
        """
        生成实际的Excel报表文件

        Args:
            processed_points: 处理后的IO点数据
            output_path: 输出文件路径

        Returns:
            生成是否成功
        """
        try:
            # 检查是否有openpyxl库
            try:
                import openpyxl
                from openpyxl.styles import Font, Alignment
            except ImportError:
                self.logger.warning("未安装openpyxl库，使用模拟生成")
                return self._simulate_excel_generation(processed_points, output_path)

            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "IO分配表"

            # 设置表头
            headers = [
                "编号", "信号类型", "卡件型号", "ETP名", "位号", "描述",
                "机柜", "机架", "槽位", "通道", "电缆名", "线对号",
                "本安", "系统", "电缆类型", "典型回路"
            ]

            # 写入表头
            for col, header in enumerate(headers, 1):
                cell = worksheet.cell(row=1, column=col, value=header)
                cell.font = Font(bold=True)
                cell.alignment = Alignment(horizontal='center')

            # 写入数据
            for row, point in enumerate(processed_points, 2):
                worksheet.cell(row=row, column=1, value=row-1)  # 编号
                worksheet.cell(row=row, column=2, value=point.get('signal_type', ''))
                worksheet.cell(row=row, column=3, value='')  # 卡件型号 - 需要从典型回路获取
                worksheet.cell(row=row, column=4, value='')  # ETP名 - 需要计算
                worksheet.cell(row=row, column=5, value=point.get('tag', ''))
                worksheet.cell(row=row, column=6, value=point.get('description', ''))
                worksheet.cell(row=row, column=7, value=point.get('cabinet', ''))
                worksheet.cell(row=row, column=8, value=point.get('rack', ''))
                worksheet.cell(row=row, column=9, value=point.get('slot', ''))
                worksheet.cell(row=row, column=10, value=point.get('channel', ''))
                worksheet.cell(row=row, column=11, value=point.get('cable_name', ''))
                worksheet.cell(row=row, column=12, value=point.get('pair_number', ''))
                worksheet.cell(row=row, column=13, value='是' if point.get('is_intrinsic') else '否')
                worksheet.cell(row=row, column=14, value=point.get('system', ''))
                worksheet.cell(row=row, column=15, value=point.get('cable_type', ''))
                worksheet.cell(row=row, column=16, value=point.get('wiring_typical', ''))

            # 调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                for cell in column:
                    try:
                        if len(str(cell.value)) > max_length:
                            max_length = len(str(cell.value))
                    except:
                        pass
                adjusted_width = min(max_length + 2, 50)
                worksheet.column_dimensions[column_letter].width = adjusted_width

            # 保存文件
            workbook.save(output_path)
            workbook.close()

            self.logger.info(f"Excel报表已保存到: {output_path}")
            return True

        except Exception as e:
            self.logger.error(f"生成Excel报表失败: {e}")
            return False

    def _simulate_excel_generation(self, processed_points: List[Dict[str, Any]], output_path: str) -> bool:
        """
        模拟Excel文件生成（当openpyxl不可用时）

        Args:
            processed_points: 处理后的IO点数据
            output_path: 输出文件路径

        Returns:
            生成是否成功
        """
        try:
            # 创建一个简单的CSV文件作为替代
            import csv

            csv_path = output_path.replace('.xlsx', '.csv')

            with open(csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)

                # 写入表头
                headers = [
                    "编号", "信号类型", "卡件型号", "ETP名", "位号", "描述",
                    "机柜", "机架", "槽位", "通道", "电缆名", "线对号",
                    "本安", "系统", "电缆类型", "典型回路"
                ]
                writer.writerow(headers)

                # 写入数据
                for i, point in enumerate(processed_points, 1):
                    row = [
                        i,
                        point.get('signal_type', ''),
                        '',  # 卡件型号
                        '',  # ETP名
                        point.get('tag', ''),
                        point.get('description', ''),
                        point.get('cabinet', ''),
                        point.get('rack', ''),
                        point.get('slot', ''),
                        point.get('channel', ''),
                        point.get('cable_name', ''),
                        point.get('pair_number', ''),
                        '是' if point.get('is_intrinsic') else '否',
                        point.get('system', ''),
                        point.get('cable_type', ''),
                        point.get('wiring_typical', '')
                    ]
                    writer.writerow(row)

            self.logger.info(f"模拟报表已保存到: {csv_path}")
            return True

        except Exception as e:
            self.logger.error(f"模拟Excel生成失败: {e}")
            return False


class ReportManager:
    """报表管理器"""
    
    def __init__(self):
        """初始化报表管理器"""
        self.logger = get_logger(__name__)
        self.templates = {}
        self.generators = {}
    
    def register_template(self, template_id: str, template: "ReportTemplate"):
        """
        注册报表模板
        
        Args:
            template_id: 模板ID
            template: 报表模板
        """
        self.templates[template_id] = template
        self.logger.info(f"注册报表模板: {template_id}")
    
    def generate_report(self, report_type: str, template_id: str, 
                       output_path: str, data_sources: Dict[str, Any]) -> bool:
        """
        生成报表
        
        Args:
            report_type: 报表类型
            template_id: 模板ID
            output_path: 输出路径
            data_sources: 数据源
            
        Returns:
            生成是否成功
        """
        try:
            if template_id not in self.templates:
                self.logger.error(f"未找到模板: {template_id}")
                return False
            
            template = self.templates[template_id]
            
            if report_type == 'io_allocation':
                builder = IOReportBuilder(template)
                
                # 添加数据源
                for name, data in data_sources.items():
                    builder.add_data_source(name, data)
                
                # 生成报表
                return builder.generate_report(output_path)
            else:
                self.logger.error(f"不支持的报表类型: {report_type}")
                return False
                
        except Exception as e:
            self.logger.error(f"生成报表失败: {e}")
            return False


class ReportGeneratorFactory:
    """报表生成器工厂"""
    
    _generators = {
        'io_allocation': IOReportBuilder
    }
    
    @classmethod
    def get_available_types(cls) -> List[str]:
        """获取可用的报表类型"""
        return list(cls._generators.keys())
    
    @classmethod
    def create_generator(cls, report_type: str, template: "ReportTemplate"):
        """
        创建报表生成器
        
        Args:
            report_type: 报表类型
            template: 报表模板
            
        Returns:
            报表生成器实例
        """
        if report_type in cls._generators:
            return cls._generators[report_type](template)
        else:
            raise ValueError(f"不支持的报表类型: {report_type}")
