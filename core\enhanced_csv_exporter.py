"""
增强的CSV导出器
支持多器件分配信息和Wire连接信息的详细导出
"""

import pandas as pd
from typing import List, Dict, Any, Optional
from pathlib import Path
from datetime import datetime

from core.logger import get_logger
from core.data_models import IOPoint
from core.component_allocation_models import ComponentAllocation
from core.wire_connection_manager import WireConnection


class EnhancedCSVExporter:
    """增强的CSV导出器"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def export_io_points_with_components(self, 
                                       io_points: List[IOPoint],
                                       output_file: str) -> bool:
        """
        导出I/O点及其器件分配信息
        
        Args:
            io_points: I/O点列表
            output_file: 输出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            # 准备导出数据
            export_data = []
            
            for io_point in io_points:
                # 基础I/O点信息
                base_info = io_point.to_csv_dict()
                
                # 如果有器件分配信息，为每个器件创建一行
                if hasattr(io_point, 'component_allocations') and io_point.component_allocations:
                    for allocation in io_point.component_allocations:
                        row_data = base_info.copy()
                        
                        # 添加器件分配信息
                        if hasattr(allocation, 'to_csv_dict'):
                            component_info = allocation.to_csv_dict()
                            row_data.update(component_info)
                        
                        export_data.append(row_data)
                else:
                    # 没有器件分配信息的I/O点
                    export_data.append(base_info)
            
            # 创建DataFrame并导出
            df = pd.DataFrame(export_data)
            
            # 重新排列列顺序，将重要信息放在前面
            preferred_columns = [
                'tag', 'signal_type', 'description', 'cable_name', 'pair_number',
                'allocated_cabinet', 'allocated_rail', 'allocation_status',
                'component_name', 'hardware_type', 'part_number', 'sequence_on_rail',
                'screw_id', 'position_start_mm', 'position_end_mm',
                'channel', 'slot', 'terminal_numbers'
            ]
            
            # 重新排列列
            available_columns = [col for col in preferred_columns if col in df.columns]
            remaining_columns = [col for col in df.columns if col not in preferred_columns]
            final_columns = available_columns + remaining_columns
            
            df = df[final_columns]
            
            # 导出到CSV
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"I/O点器件分配信息导出成功: {output_file} ({len(export_data)}行数据)")
            return True
            
        except Exception as e:
            self.logger.error(f"导出I/O点器件分配信息失败: {e}")
            return False
    
    def export_wire_connections(self, 
                              io_points: List[IOPoint],
                              output_file: str) -> bool:
        """
        导出Wire连接信息
        
        Args:
            io_points: I/O点列表
            output_file: 输出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            wire_data = []
            
            for io_point in io_points:
                if hasattr(io_point, 'wire_connections') and io_point.wire_connections:
                    for wire_info in io_point.wire_connections:
                        wire_data.append(wire_info)
            
            if not wire_data:
                self.logger.warning("没有Wire连接信息可导出")
                return False
            
            # 创建DataFrame并导出
            df = pd.DataFrame(wire_data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"Wire连接信息导出成功: {output_file} ({len(wire_data)}条连接)")
            return True
            
        except Exception as e:
            self.logger.error(f"导出Wire连接信息失败: {e}")
            return False
    
    def export_component_summary(self, 
                               io_points: List[IOPoint],
                               output_file: str) -> bool:
        """
        导出器件汇总信息
        
        Args:
            io_points: I/O点列表
            output_file: 输出文件路径
            
        Returns:
            导出是否成功
        """
        try:
            summary_data = []
            
            # 统计器件信息
            component_stats = {}
            cabinet_stats = {}
            rail_stats = {}
            
            for io_point in io_points:
                if hasattr(io_point, 'component_allocations') and io_point.component_allocations:
                    for allocation in io_point.component_allocations:
                        if hasattr(allocation, 'hardware_type'):
                            # 器件类型统计
                            hw_type = allocation.hardware_type
                            if hw_type not in component_stats:
                                component_stats[hw_type] = {'count': 0, 'part_numbers': set()}
                            component_stats[hw_type]['count'] += 1
                            if hasattr(allocation, 'part_number'):
                                component_stats[hw_type]['part_numbers'].add(allocation.part_number)
                            
                            # 机柜统计
                            cabinet = getattr(allocation, 'cabinet_name', '')
                            if cabinet:
                                if cabinet not in cabinet_stats:
                                    cabinet_stats[cabinet] = 0
                                cabinet_stats[cabinet] += 1
                            
                            # 导轨统计
                            rail = getattr(allocation, 'rail_name', '')
                            if rail:
                                rail_key = f"{cabinet}/{rail}"
                                if rail_key not in rail_stats:
                                    rail_stats[rail_key] = 0
                                rail_stats[rail_key] += 1
            
            # 生成汇总数据
            # 器件类型汇总
            for hw_type, stats in component_stats.items():
                summary_data.append({
                    'category': '器件类型',
                    'name': hw_type,
                    'count': stats['count'],
                    'details': ', '.join(stats['part_numbers'])
                })
            
            # 机柜汇总
            for cabinet, count in cabinet_stats.items():
                summary_data.append({
                    'category': '机柜',
                    'name': cabinet,
                    'count': count,
                    'details': f'{count}个器件'
                })
            
            # 导轨汇总
            for rail, count in rail_stats.items():
                summary_data.append({
                    'category': '导轨',
                    'name': rail,
                    'count': count,
                    'details': f'{count}个器件'
                })
            
            # 创建DataFrame并导出
            df = pd.DataFrame(summary_data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"器件汇总信息导出成功: {output_file} ({len(summary_data)}条记录)")
            return True
            
        except Exception as e:
            self.logger.error(f"导出器件汇总信息失败: {e}")
            return False
    
    def export_allocation_report(self, 
                               io_points: List[IOPoint],
                               output_directory: str) -> Dict[str, bool]:
        """
        导出完整的分配报告（多个文件）
        
        Args:
            io_points: I/O点列表
            output_directory: 输出目录
            
        Returns:
            各文件导出结果字典
        """
        results = {}
        output_dir = Path(output_directory)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 生成时间戳
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        try:
            # 1. 导出I/O点器件分配信息
            io_components_file = output_dir / f"IO点器件分配_{timestamp}.csv"
            results['io_components'] = self.export_io_points_with_components(
                io_points, str(io_components_file)
            )
            
            # 2. 导出Wire连接信息
            wire_connections_file = output_dir / f"Wire连接信息_{timestamp}.csv"
            results['wire_connections'] = self.export_wire_connections(
                io_points, str(wire_connections_file)
            )
            
            # 3. 导出器件汇总信息
            component_summary_file = output_dir / f"器件汇总_{timestamp}.csv"
            results['component_summary'] = self.export_component_summary(
                io_points, str(component_summary_file)
            )
            
            # 4. 导出基础I/O点信息（兼容性）
            basic_io_file = output_dir / f"IO点基础信息_{timestamp}.csv"
            results['basic_io'] = self._export_basic_io_points(
                io_points, str(basic_io_file)
            )
            
            success_count = sum(1 for success in results.values() if success)
            self.logger.info(f"分配报告导出完成: {success_count}/{len(results)} 个文件成功")
            
            return results
            
        except Exception as e:
            self.logger.error(f"导出分配报告失败: {e}")
            return results
    
    def _export_basic_io_points(self, 
                              io_points: List[IOPoint],
                              output_file: str) -> bool:
        """导出基础I/O点信息（向后兼容）"""
        try:
            data = [io_point.to_csv_dict() for io_point in io_points]
            df = pd.DataFrame(data)
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            
            self.logger.info(f"基础I/O点信息导出成功: {output_file} ({len(data)}条记录)")
            return True
            
        except Exception as e:
            self.logger.error(f"导出基础I/O点信息失败: {e}")
            return False
