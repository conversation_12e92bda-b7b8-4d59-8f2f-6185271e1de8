"""
快速修复验证脚本
验证机柜字典转换修复是否解决了"'dict' object has no attribute 'find_compatible_rails'"错误
"""

def test_cabinet_dict_conversion():
    """测试机柜字典转换功能"""
    print("🔧 快速修复验证：机柜字典转换测试")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        from core.allocator import IOAllocator
        from core.component_allocation_models import ActualCabinet

        print("✅ 模块导入成功")

        # 创建分配器（添加必需的配置参数）
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        allocator = IOAllocator(config)
        print("✅ 分配器创建成功")
        
        # 创建测试机柜字典（模拟原始数据格式）
        cabinet_dict = {
            'name': 'TEST-CABINET-001',
            'template': 'PPG BAR',
            'type': 'Marshalling',
            'location': 'Test Area',
            'rails': [
                {
                    'name': 'Rail_A',
                    'position': 'Top',
                    'length': 1000.0,
                    'io_type': 'Mixed',
                    'intrinsic': 'NIS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        }
        
        print(f"📋 测试机柜字典: {cabinet_dict['name']}")
        
        # 测试转换功能
        actual_cabinet = allocator._convert_dict_to_actual_cabinet(cabinet_dict)
        
        if actual_cabinet is None:
            print("❌ 机柜字典转换失败")
            return False
        
        print("✅ 机柜字典转换成功")
        print(f"   - 类型: {type(actual_cabinet)}")
        print(f"   - 机柜名: {actual_cabinet.name}")
        print(f"   - 导轨数: {len(actual_cabinet.rails)}")
        
        # 验证关键方法是否存在
        if hasattr(actual_cabinet, 'find_compatible_rails'):
            print("✅ find_compatible_rails方法存在")
        else:
            print("❌ find_compatible_rails方法不存在")
            return False
        
        # 测试find_compatible_rails方法
        from core.component_allocation_models import ComponentDefinition, ComponentType
        
        test_component = ComponentDefinition(
            name="TestFTB",
            component_type=ComponentType.FIELD_TERM_IN,
            part_number="TEST-FTB-001",
            width=50.0
        )
        
        try:
            compatible_rails = actual_cabinet.find_compatible_rails(test_component)
            print(f"✅ find_compatible_rails方法调用成功，找到 {len(compatible_rails)} 个兼容导轨")
            
            if len(compatible_rails) > 0:
                rail = compatible_rails[0]
                print(f"   - 兼容导轨: {rail.name}")
                print(f"   - 支持的器件类型: {rail.supported_part_types}")
                
                # 测试can_accommodate方法
                can_fit = rail.can_accommodate(test_component)
                print(f"   - 可以容纳测试器件: {can_fit}")
                
        except Exception as e:
            print(f"❌ find_compatible_rails方法调用失败: {e}")
            return False
        
        print("\n🎉 机柜字典转换修复验证成功！")
        print("✅ 原始错误 \"'dict' object has no attribute 'find_compatible_rails'\" 已解决")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_component_allocation_flow():
    """测试完整的器件分配流程"""
    print("\n" + "=" * 50)
    print("🔧 完整分配流程测试")
    
    try:
        from core.data_models import IOPoint, SignalType
        from core.allocator import IOAllocator

        # 创建测试I/O点
        test_io_point = IOPoint(
            tag="TEST-AI-001",
            signal_type=SignalType.AI,
            description="Test Analog Input",
            location="Test Location",
            cable_name="TEST-CABLE-001",
            pair_number=1,
            is_intrinsic=True,
            system="Test System",
            cable_type="Instrumentation",
            wiring_typical="AI IS BABP"  # 使用实际存在的典型回路
        )
        
        print(f"📍 测试I/O点: {test_io_point.tag}")
        print(f"   - 信号类型: {test_io_point.signal_type.value}")
        print(f"   - 典型回路: {test_io_point.wiring_typical}")
        
        # 创建分配器（添加必需的配置参数）
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            },
            'naming_settings': {
                'rack_prefix': 'R',
                'enable_auto_naming': True
            }
        }
        allocator = IOAllocator(config)
        
        # 创建测试机柜字典
        cabinet_dict = {
            'name': 'E-1001',
            'template': 'PPG BAR',
            'type': 'Marshalling',
            'location': 'Area 1',
            'rails': [
                {
                    'name': 'Rail_A',
                    'position': 'Top',
                    'length': 2000.0,
                    'io_type': 'Analog',
                    'intrinsic': 'IS',
                    'voltage_level': 24,
                    'PartType01': 'FieldTermIn',
                    'PartType02': 'FieldTermOut',
                    'PartType03': 'ETP',
                    'PartType04': 'Barrier',
                    'reserved_from': 0.0,
                    'reserved_to': 0.0
                }
            ]
        }
        
        # 测试单点分配
        print("\n📋 测试单点分配...")
        success = allocator._allocate_single_point(
            test_io_point, 
            cabinet_dict, 
            {}  # 空的典型回路字典，实际会使用内部管理器
        )
        
        if success:
            print("✅ 单点分配成功")
            print(f"   - 分配状态: {test_io_point.allocation_status}")
            print(f"   - 分配机柜: {test_io_point.allocated_cabinet}")
            
            # 检查器件分配信息
            if hasattr(test_io_point, 'component_allocations') and test_io_point.component_allocations:
                print(f"   - 分配器件数: {len(test_io_point.component_allocations)}")
                for i, allocation in enumerate(test_io_point.component_allocations):
                    if hasattr(allocation, 'component_name'):
                        print(f"     * 器件{i+1}: {allocation.component_name}")
            else:
                print("   - 器件分配信息: 无")
        else:
            print("❌ 单点分配失败")
            return False
        
        print("\n🎉 完整分配流程测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 完整分配流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 机柜字典转换修复快速验证")
    print("解决错误: 'dict' object has no attribute 'find_compatible_rails'")
    print("=" * 60)
    
    # 测试1: 机柜字典转换
    conversion_success = test_cabinet_dict_conversion()
    
    # 测试2: 完整分配流程
    if conversion_success:
        flow_success = test_component_allocation_flow()
    else:
        flow_success = False
    
    print("\n" + "=" * 60)
    if conversion_success and flow_success:
        print("🎉 修复验证完全成功！")
        print("✅ 原始错误已解决，I/O点分配系统现在应该可以正常工作")
        print("🔄 建议：重新运行完整的I/O点分配功能进行最终验证")
    else:
        print("⚠️  修复验证发现问题，需要进一步调试")
        
    print("\n📝 如需详细调试信息，请查看相关日志文件")
