#!/usr/bin/env python3
"""
测试机柜位置修复效果
验证器件根据其在典型回路中的位置正确分配到对应的机柜类型
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.logger import get_logger
from core.component_allocation_engine import ComponentAllocationEngine
from core.component_allocation_models import ActualCabinet, RailInstance, ComponentDefinition, ComponentType
from core.wiring_typical_parser import WiringTypicalParser
from core.data_models import IOPoint, SignalType

def test_typical_parsing_with_cabinet_location():
    """测试典型回路解析时的机柜位置记录"""
    print("=== 测试典型回路解析中的机柜位置记录 ===")
    
    try:
        parser = WiringTypicalParser()
        
        # 测试 DI NIS N.xml 文件（包含 3000510-380C1R）
        typical_file = Path("01C_Wiring Typical/DI NIS N.xml")
        if not typical_file.exists():
            print(f"❌ 典型回路文件不存在: {typical_file}")
            return False
        
        typical_def = parser.parse_wiring_typical_file(str(typical_file))
        
        print(f"✅ 成功解析典型回路: {typical_def.name}")
        print(f"   汇控柜器件数量: {len(typical_def.marshalling_components)}")
        print(f"   系统柜器件数量: {len(typical_def.system_components)}")
        
        # 检查汇控柜器件的机柜位置
        print("\n汇控柜器件列表:")
        for component in typical_def.marshalling_components:
            cabinet_location = getattr(component, 'cabinet_location', 'Unknown')
            print(f"  - {component.name} ({component.component_type.value}) -> 机柜位置: {cabinet_location}")
            
            # 特别检查 3000510-380C1R
            if component.name == "3000510-380C1R":
                if cabinet_location == "MarshallingCabinet":
                    print(f"    ✅ 3000510-380C1R 正确记录为 MarshallingCabinet 位置")
                else:
                    print(f"    ❌ 3000510-380C1R 机柜位置错误: {cabinet_location}")
                    return False
        
        # 检查系统柜器件的机柜位置
        if typical_def.system_components:
            print("\n系统柜器件列表:")
            for component in typical_def.system_components:
                cabinet_location = getattr(component, 'cabinet_location', 'Unknown')
                print(f"  - {component.name} ({component.component_type.value}) -> 机柜位置: {cabinet_location}")
        
        return True
        
    except Exception as e:
        print(f"❌ 典型回路解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_cabinet_type_determination():
    """测试基于机柜位置的机柜类型确定"""
    print("\n=== 测试基于机柜位置的机柜类型确定 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        
        # 创建测试器件，模拟不同的机柜位置
        test_components = [
            ComponentDefinition(
                name="3000510-380C1R",
                component_type=ComponentType.ETP,
                part_number="3000510-380C1R",
                cabinet_location="MarshallingCabinet",
                width=25.0
            ),
            ComponentDefinition(
                name="FTB1",
                component_type=ComponentType.FIELD_TERM_IN,
                part_number="FTB-1",
                cabinet_location="MarshallingCabinet",
                width=12.5
            ),
            ComponentDefinition(
                name="TCN_Main_Chassis",
                component_type=ComponentType.CHASSIS,
                part_number="TCN-Main",
                cabinet_location="SystemCabinet",
                width=100.0
            ),
            ComponentDefinition(
                name="CPM16-AI3700",
                component_type=ComponentType.ETP,
                part_number="CPM16-AI3700",
                cabinet_location="SystemCabinet",
                width=12.5
            )
        ]
        
        # 测试机柜类型确定
        expected_results = [
            ("3000510-380C1R", "MarshallingCabinet", "Marshalling"),
            ("FTB1", "MarshallingCabinet", "Marshalling"),
            ("TCN_Main_Chassis", "SystemCabinet", "System"),
            ("CPM16-AI3700", "SystemCabinet", "System")
        ]
        
        all_correct = True
        for i, component in enumerate(test_components):
            actual_cabinet_type = engine._determine_required_cabinet_type(component)
            component_name, exp_location, exp_cabinet_type = expected_results[i]

            if actual_cabinet_type == exp_cabinet_type:
                print(f"✅ {component.name} ({component.component_type.value}) 位于 {component.cabinet_location} -> 需要 {actual_cabinet_type} 机柜")
            else:
                print(f"❌ {component.name} ({component.component_type.value}) 位于 {component.cabinet_location} -> 需要 {actual_cabinet_type} 机柜 (期望: {exp_cabinet_type})")
                all_correct = False
        
        return all_correct
        
    except Exception as e:
        print(f"❌ 机柜类型确定测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compatibility_with_new_logic():
    """测试新逻辑下的兼容性检查"""
    print("\n=== 测试新逻辑下的兼容性检查 ===")
    
    try:
        config = {
            'allocation_settings': {
                'enable_parttype_matching': True,
                'enable_detailed_logging': True
            }
        }
        
        engine = ComponentAllocationEngine(config)
        
        # 创建测试机柜
        marshalling_cabinet = ActualCabinet(
            name="1105-SIS-REL-101",  # 真实机柜名称
            template_name="PPG BAR",  # 模板名称
            location="Field Area",
            cabinet_type="Marshalling",
            rails=[RailInstance(
                name="Rail_MAR",
                position="Marshalling",
                length=2000.0,
                io_type="Mixed",
                intrinsic="NIS",
                voltage_level=24,
                supported_part_types=["FieldTermIn", "FieldTermOut", "ETP", "Barrier"]
            )]
        )
        
        system_cabinet = ActualCabinet(
            name="1105-SIS-SYS-201",  # 真实机柜名称
            template_name="PPG SYS",  # 模板名称
            location="Control Room",
            cabinet_type="System",
            rails=[RailInstance(
                name="Rail_SYS",
                position="System",
                length=2000.0,
                io_type="Mixed",
                intrinsic="NIS",
                voltage_level=24,
                supported_part_types=["ETP", "IOModule", "Chassis"]
            )]
        )
        
        # 测试 3000510-380C1R 器件（ETP类型但位于MarshallingCabinet）
        etp_in_marshalling = ComponentDefinition(
            name="3000510-380C1R",
            component_type=ComponentType.ETP,
            part_number="3000510-380C1R",
            cabinet_location="MarshallingCabinet",
            width=25.0
        )
        
        # 测试兼容性
        compatible_with_marshalling = engine._validate_component_cabinet_compatibility(etp_in_marshalling, marshalling_cabinet)
        compatible_with_system = engine._validate_component_cabinet_compatibility(etp_in_marshalling, system_cabinet)
        
        print(f"3000510-380C1R (ETP, 位于MarshallingCabinet):")
        print(f"  与辅助柜兼容: {compatible_with_marshalling} (期望: True)")
        print(f"  与系统柜兼容: {compatible_with_system} (期望: False)")
        
        # 验证机柜名称显示
        print(f"\n机柜名称显示测试:")
        print(f"  辅助柜实际名称: {marshalling_cabinet.name}")
        print(f"  系统柜实际名称: {system_cabinet.name}")
        
        success = (compatible_with_marshalling == True and compatible_with_system == False)
        return success
        
    except Exception as e:
        print(f"❌ 兼容性检查测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始测试机柜位置修复效果...")
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_typical_parsing_with_cabinet_location())
    test_results.append(test_cabinet_type_determination())
    test_results.append(test_compatibility_with_new_logic())
    
    # 统计结果
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！机柜位置修复成功。")
        print("\n修复效果总结：")
        print("✅ 1. 器件解析时正确记录机柜位置信息")
        print("✅ 2. 3000510-380C1R 等 ETP 器件根据位置正确分配到 Marshalling 机柜")
        print("✅ 3. 机柜类型确定基于器件位置而非 HardwareType")
        print("✅ 4. 兼容性检查使用新的机柜位置逻辑")
        print("✅ 5. 机柜名称显示真实名称而非模板名称")
        return True
    else:
        print("⚠️  部分测试失败，需要进一步检查。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
