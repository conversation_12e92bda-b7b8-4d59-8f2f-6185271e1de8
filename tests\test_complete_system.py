#!/usr/bin/env python3
"""
完整系统测试
测试I/O分配系统的完整工作流程
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_complete_workflow():
    """测试完整的工作流程"""
    try:
        print("=== EWReborn 完整系统测试 ===")
        
        # 1. 测试配置管理
        print("\n1. 测试配置管理...")
        from utils.config_manager_simple import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.load_config()
        print(f"✓ 配置加载成功，应用名称: {config.get('application.name')}")
        
        # 2. 测试日志系统
        print("\n2. 测试日志系统...")
        from core.logger import setup_logger, get_logger
        setup_logger(config.get('logging', {}))
        logger = get_logger(__name__)
        logger.info("系统测试开始")
        print("✓ 日志系统初始化成功")
        
        # 3. 测试数据加载器
        print("\n3. 测试数据加载器...")
        from core.data_loader_simple import DataLoader
        data_loader = DataLoader(config)
        all_data = data_loader.load_all_data()
        print(f"✓ 数据加载完成，状态: {all_data['load_status']}")
        
        # 4. 测试数据模型创建
        print("\n4. 测试数据模型创建...")
        models = data_loader.create_data_models(
            all_data.get('iodb', {}),
            all_data.get('pidb', {}),
            all_data.get('cabinet_profiles', {}),
            all_data.get('wiring_typicals', {})
        )
        print(f"✓ 数据模型创建成功:")
        print(f"  - I/O点: {len(models['io_points'])}")
        print(f"  - 电缆: {len(models['cables'])}")
        print(f"  - 机柜: {len(models['cabinets'])}")
        print(f"  - 典型回路: {len(models['wiring_typicals'])}")
        
        # 5. 测试数据验证
        print("\n5. 测试数据验证...")
        from core.validator import IODBValidator
        validator = IODBValidator(config)
        
        # 创建测试用的IODB数据
        test_iodb_data = {
            'io_points': models['io_points'],
            'cables': {cable.name: cable for cable in models['cables']},
            'summary': {
                'total_points': len(models['io_points']),
                'total_cables': len(models['cables'])
            }
        }
        
        validation_result = validator.validate_iodb_data(test_iodb_data)
        print(f"✓ 数据验证完成，结果: {'成功' if validation_result.success else '失败'}")
        if validation_result.errors:
            print(f"  - 错误数: {len(validation_result.errors)}")
        if validation_result.warnings:
            print(f"  - 警告数: {len(validation_result.warnings)}")
        
        # 6. 测试I/O分配算法
        print("\n6. 测试I/O分配算法...")
        from core.allocator import IOAllocator
        allocator = IOAllocator(config)
        
        allocation_result = allocator.allocate_io_points(
            models['cables'],
            models['cabinets'],
            {wt.name: {'components': []} for wt in models['wiring_typicals']}
        )
        
        print(f"✓ I/O分配完成，结果: {'成功' if allocation_result.success else '失败'}")
        if allocation_result.summary:
            summary = allocation_result.summary
            print(f"  - 总点数: {summary.get('total_points', 0)}")
            print(f"  - 成功分配: {summary.get('allocated_count', 0)}")
            print(f"  - 分配失败: {summary.get('failed_count', 0)}")
            print(f"  - 成功率: {summary.get('success_rate', 0):.1f}%")
        
        # 7. 测试结果导出准备
        print("\n7. 测试结果导出准备...")
        export_data = {
            'main_data': [
                {
                    'Tag': point.tag,
                    'Cable': point.cable_name,
                    'Status': point.allocation_status
                }
                for point in allocation_result.allocated_points + allocation_result.failed_points
            ],
            'summary_data': [
                {'项目': '测试项目', '数值': '测试值'}
            ],
            'issues_data': [
                {'类型': '测试', '描述': '测试描述'}
            ]
        }
        print(f"✓ 导出数据准备完成，主数据: {len(export_data['main_data'])}条记录")
        
        print("\n=== 完整系统测试通过 ===")
        print("所有核心功能模块工作正常！")
        
        return True
        
    except Exception as e:
        print(f"\n✗ 系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_gui_components():
    """测试GUI组件（不启动窗口）"""
    try:
        print("\n=== GUI组件测试 ===")
        
        # 测试GUI组件导入
        from PySide6.QtWidgets import QApplication
        from utils.config_manager_simple import ConfigManager
        
        # 创建应用程序实例（但不显示）
        app = QApplication.instance()
        if app is None:
            app = QApplication(sys.argv)
        
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        # 测试主窗口创建
        from gui.main_window import MainWindow
        main_window = MainWindow(config)
        print("✓ 主窗口创建成功")
        
        # 测试分配组件
        from gui.allocation_widget import AllocationWidget
        allocation_widget = AllocationWidget(config)
        print("✓ 分配界面组件创建成功")
        
        # 测试进度组件
        from gui.progress_widget import ProgressWidget
        progress_widget = ProgressWidget()
        print("✓ 进度显示组件创建成功")
        
        print("✓ GUI组件测试通过")
        return True
        
    except Exception as e:
        print(f"✗ GUI组件测试失败: {e}")
        return False

if __name__ == "__main__":
    print("EWReborn 完整系统测试")
    print("=" * 50)
    
    # 测试核心功能
    core_success = test_complete_workflow()
    
    # 测试GUI组件
    gui_success = test_gui_components()
    
    print("\n" + "=" * 50)
    if core_success and gui_success:
        print("🎉 所有测试通过！系统可以正常运行。")
        print("\n可以运行以下命令启动应用程序:")
        print("python main.py")
    else:
        print("❌ 部分测试失败，请检查错误信息。")
        if core_success:
            print("✓ 核心功能正常")
        if gui_success:
            print("✓ GUI组件正常")
